<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <version>1.1.20</version>
    </parent>

    <artifactId>was-graphql-client</artifactId>
    <name>Graphql Client for WAS</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <apollo.client.version>2.5.14</apollo.client.version>
        <okio.version>2.10.0</okio.version>
<!--        <kotlinstdlib.version>1.8.10</kotlinstdlib.version>-->
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio-multiplatform</artifactId>
            <version>${okio.version}</version>
        </dependency>
        <dependency>
            <groupId>com.apollographql.apollo</groupId>
            <artifactId>apollo-runtime</artifactId>
            <version>${apollo.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.apollographql.apollo</groupId>
            <artifactId>apollo-api</artifactId>
            <version>${apollo.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio-multiplatform</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.apollographql.apollo</groupId>
            <artifactId>apollo-reactor-support</artifactId>
            <version>${apollo.client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/mockwebserver -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>4.11.0</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.aoudiamoncef</groupId>
                <artifactId>apollo-client-maven-plugin</artifactId>
                <version>4.0.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <services>
                                <!--Complete default configuration-->

                                <itm-api>
                                    <enabled>true</enabled>
                                    <addSourceRoot>true</addSourceRoot>
                                    <sourceFolder>${project.basedir}/src/main/resources/graphql/itm</sourceFolder>
                                    <schemaPath>${project.basedir}/src/main/resources/graphql/itm/schema.json</schemaPath>
                                    <includes>
                                        <include>**/*.graphql</include>
                                        <include>**/*.gql</include>
                                        <include>**/*.json</include>
                                        <include>**/*.sdl"</include>
                                    </includes>
                                    <excludes></excludes>

                                    <compilationUnit>
                                        <name>itm</name>
                                        <outputDirectory>
                                            ${project.build.directory}/generated-sources/graphql-client/itm/
                                        </outputDirectory>
                                        <generateOperationDescriptors>false</generateOperationDescriptors>
                                        <operationOutputFile>
                                            ${project.build.directory}/generated/operationOutput/apollo/itm/operationOutput.json
                                        </operationOutputFile>

                                        <compilerParams>
                                            <rootFolders>
                                                <rootFolder>${project.basedir}/src/main/resources/graphql/itm/</rootFolder>
                                            </rootFolders>
                                            <generateKotlinModels>false</generateKotlinModels>
                                            <warnOnDeprecatedUsages>true</warnOnDeprecatedUsages>
                                            <failOnWarnings>false</failOnWarnings>
                                            <generateOperationOutput>true</generateOperationOutput>
                                            <customTypeMapping></customTypeMapping>
                                            <operationIdGeneratorClass>
                                                com.apollographql.apollo.compiler.OperationIdGenerator$Sha256
                                            </operationIdGeneratorClass>
                                            <suppressRawTypesWarning>false</suppressRawTypesWarning>
                                            <useSemanticNaming>true</useSemanticNaming>
                                            <nullableValueType>ANNOTATED</nullableValueType>
                                            <generateModelBuilder>false</generateModelBuilder>
                                            <useJavaBeansSemanticNaming>false</useJavaBeansSemanticNaming>
                                            <generateVisitorForPolymorphicDatatypes>false</generateVisitorForPolymorphicDatatypes>
                                            <rootPackageName>com.intuit.itm.entity.graphql</rootPackageName>
                                            <packageName></packageName>
                                            <generateAsInternal>false</generateAsInternal>
                                            <sealedClassesForEnumsMatching></sealedClassesForEnumsMatching>
                                            <generateApolloMetadata>false</generateApolloMetadata>
                                            <metadataOutputFile>
                                                ${project.build.directory}/generated/metadata/apollo/itm/metadata.json
                                            </metadataOutputFile>
                                            <alwaysGenerateTypesMatching></alwaysGenerateTypesMatching>
                                            <kotlinMultiPlatformProject>false</kotlinMultiPlatformProject>
                                        </compilerParams>
                                    </compilationUnit>

                                    <introspection>
                                        <enabled>false</enabled>
                                        <endpointUrl></endpointUrl>
                                        <headers></headers>
                                        <schemaFile>${project.basedir}/src/main/resources/graphql/itm/schema.json</schemaFile>
                                        <connectTimeoutSeconds>10</connectTimeoutSeconds>
                                        <readTimeoutSeconds>10</readTimeoutSeconds>
                                        <writeTimeoutSeconds>10</writeTimeoutSeconds>
                                        <useSelfSignedCertificat>false</useSelfSignedCertificat>
                                        <useGzip>false</useGzip>
                                        <prettyPrint>false</prettyPrint>
                                    </introspection>
                                </itm-api>

                                    <variability-api>
                                        <enabled>true</enabled>
                                        <addSourceRoot>true</addSourceRoot>
                                        <sourceFolder>${project.basedir}/src/main/resources/graphql/variability</sourceFolder>
                                        <schemaPath>${project.basedir}/src/main/resources/graphql/variability/schema.json</schemaPath>
                                        <includes>
                                            <include>**/*.graphql</include>
                                            <include>**/*.gql</include>
                                            <include>**/*.json</include>
                                            <include>**/*.sdl"</include>
                                        </includes>
                                        <excludes></excludes>

                                        <compilationUnit>
                                            <name>variability</name>
                                            <outputDirectory>
                                                ${project.build.directory}/generated-sources/graphql-client/variability/
                                            </outputDirectory>
                                            <generateOperationDescriptors>false</generateOperationDescriptors>
                                            <operationOutputFile>
                                                ${project.build.directory}/generated/operationOutput/apollo/variability/operationOutput.json
                                            </operationOutputFile>

                                            <compilerParams>
                                                <rootFolders>
                                                    <rootFolder>${project.basedir}/src/main/resources/graphql/variability/</rootFolder>
                                                </rootFolders>
                                                <generateKotlinModels>false</generateKotlinModels>
                                                <warnOnDeprecatedUsages>true</warnOnDeprecatedUsages>
                                                <failOnWarnings>false</failOnWarnings>
                                                <generateOperationOutput>true</generateOperationOutput>
                                                <customTypeMapping></customTypeMapping>
                                                <operationIdGeneratorClass>
                                                    com.apollographql.apollo.compiler.OperationIdGenerator$Sha256
                                                </operationIdGeneratorClass>
                                                <suppressRawTypesWarning>false</suppressRawTypesWarning>
                                                <useSemanticNaming>true</useSemanticNaming>
                                                <nullableValueType>ANNOTATED</nullableValueType>
                                                <generateModelBuilder>false</generateModelBuilder>
                                                <useJavaBeansSemanticNaming>false</useJavaBeansSemanticNaming>
                                                <generateVisitorForPolymorphicDatatypes>false</generateVisitorForPolymorphicDatatypes>
                                                <rootPackageName>com.intuit.variability.entity.graphql</rootPackageName>
                                                <packageName></packageName>
                                                <generateAsInternal>false</generateAsInternal>
                                                <sealedClassesForEnumsMatching></sealedClassesForEnumsMatching>
                                                <generateApolloMetadata>false</generateApolloMetadata>
                                                <metadataOutputFile>
                                                    ${project.build.directory}/generated/metadata/apollo/variability/metadata.json
                                                </metadataOutputFile>
                                                <alwaysGenerateTypesMatching></alwaysGenerateTypesMatching>
                                                <kotlinMultiPlatformProject>false</kotlinMultiPlatformProject>
                                            </compilerParams>
                                        </compilationUnit>

                                        <introspection>
                                            <enabled>false</enabled>
                                            <endpointUrl></endpointUrl>
                                            <headers></headers>
                                            <schemaFile>${project.basedir}/src/main/resources/graphql/variability/schema.json</schemaFile>
                                            <connectTimeoutSeconds>10</connectTimeoutSeconds>
                                            <readTimeoutSeconds>10</readTimeoutSeconds>
                                            <writeTimeoutSeconds>10</writeTimeoutSeconds>
                                            <useSelfSignedCertificat>false</useSelfSignedCertificat>
                                            <useGzip>false</useGzip>
                                            <prettyPrint>false</prettyPrint>
                                        </introspection>
                                    </variability-api>


                                <identity-api>
                                    <enabled>true</enabled>
                                    <addSourceRoot>true</addSourceRoot>
                                    <sourceFolder>${project.basedir}/src/main/resources/graphql/identity</sourceFolder>
                                    <schemaPath>${project.basedir}/src/main/resources/graphql/identity/schema.json</schemaPath>
                                    <includes>
                                        <include>**/*.graphql</include>
                                        <include>**/*.gql</include>
                                        <include>**/*.json</include>
                                        <include>**/*.sdl"</include>
                                    </includes>
                                    <excludes></excludes>

                                    <compilationUnit>
                                        <name>identity</name>
                                        <outputDirectory>
                                            ${project.build.directory}/generated-sources/graphql-client/identity/
                                        </outputDirectory>
                                        <generateOperationDescriptors>false</generateOperationDescriptors>
                                        <operationOutputFile>
                                            ${project.build.directory}/generated/operationOutput/apollo/identity/operationOutput.json
                                        </operationOutputFile>

                                        <compilerParams>
                                            <rootFolders>
                                                <rootFolder>${project.basedir}/src/main/resources/graphql/identity/</rootFolder>
                                            </rootFolders>
                                            <generateKotlinModels>false</generateKotlinModels>
                                            <warnOnDeprecatedUsages>true</warnOnDeprecatedUsages>
                                            <failOnWarnings>false</failOnWarnings>
                                            <generateOperationOutput>true</generateOperationOutput>
                                            <customTypeMapping></customTypeMapping>
                                            <operationIdGeneratorClass>
                                                com.apollographql.apollo.compiler.OperationIdGenerator$Sha256
                                            </operationIdGeneratorClass>
                                            <suppressRawTypesWarning>false</suppressRawTypesWarning>
                                            <useSemanticNaming>true</useSemanticNaming>
                                            <nullableValueType>ANNOTATED</nullableValueType>
                                            <generateModelBuilder>false</generateModelBuilder>
                                            <useJavaBeansSemanticNaming>false</useJavaBeansSemanticNaming>
                                            <generateVisitorForPolymorphicDatatypes>false</generateVisitorForPolymorphicDatatypes>
                                            <rootPackageName>com.intuit.identity.entity.graphql</rootPackageName>
                                            <packageName></packageName>
                                            <generateAsInternal>false</generateAsInternal>
                                            <sealedClassesForEnumsMatching></sealedClassesForEnumsMatching>
                                            <generateApolloMetadata>false</generateApolloMetadata>
                                            <metadataOutputFile>
                                                ${project.build.directory}/generated/metadata/apollo/identity/metadata.json
                                            </metadataOutputFile>
                                            <alwaysGenerateTypesMatching></alwaysGenerateTypesMatching>
                                            <kotlinMultiPlatformProject>false</kotlinMultiPlatformProject>
                                        </compilerParams>
                                    </compilationUnit>

                                    <introspection>
                                        <enabled>false</enabled>
                                        <endpointUrl></endpointUrl>
                                        <headers></headers>
                                        <schemaFile>${project.basedir}/src/main/resources/graphql/identity/schema.json</schemaFile>
                                        <connectTimeoutSeconds>10</connectTimeoutSeconds>
                                        <readTimeoutSeconds>10</readTimeoutSeconds>
                                        <writeTimeoutSeconds>10</writeTimeoutSeconds>
                                        <useSelfSignedCertificat>false</useSelfSignedCertificat>
                                        <useGzip>false</useGzip>
                                        <prettyPrint>false</prettyPrint>
                                    </introspection>
                                </identity-api>

                                <test-api>
                                    <enabled>true</enabled>
                                    <addSourceRoot>true</addSourceRoot>
                                    <sourceFolder>${project.basedir}/src/main/resources/graphql/test</sourceFolder>
                                    <schemaPath>${project.basedir}/src/main/resources/graphql/test/schema.json</schemaPath>
                                    <includes>
                                        <include>**/*.graphql</include>
                                        <include>**/*.gql</include>
                                        <include>**/*.json</include>
                                        <include>**/*.sdl"</include>
                                    </includes>
                                    <excludes></excludes>

                                    <compilationUnit>
                                        <name>identity</name>
                                        <outputDirectory>
                                            ${project.build.directory}/generated-sources/graphql-client/test/
                                        </outputDirectory>
                                        <generateOperationDescriptors>false</generateOperationDescriptors>
                                        <operationOutputFile>
                                            ${project.build.directory}/generated/operationOutput/apollo/test/operationOutput.json
                                        </operationOutputFile>

                                        <compilerParams>
                                            <rootFolders>
                                                <rootFolder>${project.basedir}/src/main/resources/graphql/test/</rootFolder>
                                            </rootFolders>
                                            <generateKotlinModels>false</generateKotlinModels>
                                            <warnOnDeprecatedUsages>true</warnOnDeprecatedUsages>
                                            <failOnWarnings>false</failOnWarnings>
                                            <generateOperationOutput>true</generateOperationOutput>
                                            <customTypeMapping></customTypeMapping>
                                            <operationIdGeneratorClass>
                                                com.apollographql.apollo.compiler.OperationIdGenerator$Sha256
                                            </operationIdGeneratorClass>
                                            <suppressRawTypesWarning>false</suppressRawTypesWarning>
                                            <useSemanticNaming>true</useSemanticNaming>
                                            <nullableValueType>ANNOTATED</nullableValueType>
                                            <generateModelBuilder>false</generateModelBuilder>
                                            <useJavaBeansSemanticNaming>false</useJavaBeansSemanticNaming>
                                            <generateVisitorForPolymorphicDatatypes>false</generateVisitorForPolymorphicDatatypes>
                                            <rootPackageName>com.intuit.test.entity.graphql</rootPackageName>
                                            <packageName></packageName>
                                            <generateAsInternal>false</generateAsInternal>
                                            <sealedClassesForEnumsMatching></sealedClassesForEnumsMatching>
                                            <generateApolloMetadata>false</generateApolloMetadata>
                                            <metadataOutputFile>
                                                ${project.build.directory}/generated/metadata/apollo/test/metadata.json
                                            </metadataOutputFile>
                                            <alwaysGenerateTypesMatching></alwaysGenerateTypesMatching>
                                            <kotlinMultiPlatformProject>false</kotlinMultiPlatformProject>
                                        </compilerParams>
                                    </compilationUnit>

                                    <introspection>
                                        <enabled>false</enabled>
                                        <endpointUrl></endpointUrl>
                                        <headers></headers>
                                        <schemaFile>${project.basedir}/src/main/resources/graphql/test/schema.json</schemaFile>
                                        <connectTimeoutSeconds>10</connectTimeoutSeconds>
                                        <readTimeoutSeconds>10</readTimeoutSeconds>
                                        <writeTimeoutSeconds>10</writeTimeoutSeconds>
                                        <useSelfSignedCertificat>false</useSelfSignedCertificat>
                                        <useGzip>false</useGzip>
                                        <prettyPrint>false</prettyPrint>
                                    </introspection>
                                </test-api>

                            </services>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>