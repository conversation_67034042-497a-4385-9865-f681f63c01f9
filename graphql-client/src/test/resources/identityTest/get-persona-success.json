{"data": {"account": {"__typename": "Account", "id": "****************", "status": "ACTIVE", "accountType": "ORGANIZATION", "namespaceId": null, "accountProfile": {"__typename": "Profile", "id": "b8707d37-dd2b-3f57-a134-7aee4178d22b", "profileType": "ORGANIZATION", "profileStatus": "ACTIVE"}, "profiles": {"__typename": "ProfileConnection", "edges": [{"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "accountId": "****************", "profileType": "PERSON", "displayName": "Company - SKS", "claimedBy": "****************", "accountRelationships": ["EMPLOYEE"], "profileStatus": "ACTIVE", "preferences": null, "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "emails": [{"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "****************", "email": "<EMAIL>"}], "phoneNumbers": []}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "************************"}], "pageInfo": {"__typename": "PageInfo", "endCursor": "************************", "hasNextPage": false}}}}}