{"data": {"profileSearch": {"__typename": "ProfileSearchConnection", "edges": [{"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "e62d7318-718f-3228-891d-882d21160166", "accountId": "****************", "profileStatus": "ACTIVE", "profileType": "PERSON", "displayName": null, "claimedBy": "2022-34b6c7a2-216e-47aa-9df6-14d96f0e450c", "pseudonymId": "129e0d8832d049647d5bf7dd2ba53163276", "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "phoneNumbers": [], "emails": [{"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "****************", "email": "<EMAIL>"}]}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "T1RFek1ETTFPRFl4TWpBd09USTJOZz09OlYx"}, {"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "accountId": "****************", "profileStatus": "ACTIVE", "profileType": "PERSON", "displayName": "Company_GB_OBI-LL1_20001274_20-10-2022", "claimedBy": "****************", "pseudonymId": "129c0d34a9151b447cabe57b5d5209f2a44", "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "phoneNumbers": [], "emails": [{"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "****************", "email": "<EMAIL>"}]}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "T1RFek1ETTFPRFl4TWpBd09UTXdOZz09OlYx"}, {"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "accountId": "*****************", "profileStatus": "ACTIVE", "profileType": "PERSON", "displayName": "test1686120795109_iamtestpass's Company", "claimedBy": "****************", "pseudonymId": "129bf50fba0dfa94bb7a6b64bcc6ce38a28", "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "phoneNumbers": [], "emails": [{"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "****************", "email": "<EMAIL>"}]}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "T1RFek1ETTJNRFV5TnpZMU5qUTBOZz09OlYx"}, {"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "accountId": "*****************", "profileStatus": "ACTIVE", "profileType": "PERSON", "displayName": "Account 2", "claimedBy": "****************", "pseudonymId": "12982223899426747629ef0f98e23cf8f79", "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "phoneNumbers": [], "emails": [{"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "****************", "email": "<EMAIL>"}]}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "T1RFek1ETTJNRFUzTWpJd05URXhOZz09OlYx"}], "pageInfo": {"__typename": "PageInfo", "endCursor": "T1RFek1ETTJNRFUzTWpJd05URXhOZz09OlYx", "hasNextPage": true}}}}