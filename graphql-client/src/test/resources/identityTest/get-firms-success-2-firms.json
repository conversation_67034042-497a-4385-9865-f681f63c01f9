{"data": {"account": {"__typename": "Account", "profiles": {"__typename": "ProfileConnection", "edges": [{"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "mirrorAccountId": "****************", "trustGrants": [{"__typename": "TrustGrant", "authorizationGrantsScope": {"__typename": "AuthorizationGrantsScope", "roles": ["Intuit.sb.manageSubscription", "Intuit.sb.accountant.qbo", "Intuit.sb.manageCompany", "Intuit.sb.accountant.pro", "Intuit.sb.accountant.allaccess", "Intuit.sb.accountant.companyadmin", "Intuit.sb.manageUsers"]}, "assignments": [{"__typename": "AssignmentGrant", "profile": {"__typename": "Profile", "displayName": "test1686120795109_iamtestpass's Company", "id": "****************"}}]}], "accountRelationships": ["FIRM"], "profileType": "ORGANIZATION", "businessInfo": {"__typename": "BusinessInfo", "primaryPointOfContact": {"__typename": "PrimaryPointOfContact", "email": {"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}}}}, {"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "mirrorAccountId": "****************", "trustGrants": [{"__typename": "TrustGrant", "authorizationGrantsScope": {"__typename": "AuthorizationGrantsScope", "roles": ["Intuit.sb.manageSubscription", "Intuit.sb.accountant.qbo", "Intuit.sb.manageCompany", "Intuit.sb.accountant.pro", "Intuit.sb.accountant.allaccess", "Intuit.sb.accountant.companyadmin", "Intuit.sb.manageUsers"]}, "assignments": [{"__typename": "AssignmentGrant", "profile": {"__typename": "Profile", "displayName": "Account 2", "id": "****************"}}]}], "accountRelationships": ["FIRM"], "profileType": "ORGANIZATION", "businessInfo": {"__typename": "BusinessInfo", "primaryPointOfContact": {"__typename": "PrimaryPointOfContact", "email": {"__typename": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}}}}}]}, "id": "****************"}}}