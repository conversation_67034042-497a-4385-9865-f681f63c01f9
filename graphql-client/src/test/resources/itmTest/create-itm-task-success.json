{"data": {"taskManagementCreateTask": {"__typename": "TaskManagement_CreateTaskResponse", "task": {"__typename": "TaskManagement_Task", "id": 1, "name": "Test task Invoice - 234", "description": "This is a test task", "status": "Inprogress", "dueDate": "2023-12-29T00:00:00.234Z", "priority": null, "type": "QB_INVOICE_APPROVAL", "assignee": "expert1", "references": [{"__typename": "TaskManagement_Reference", "referenceType": "INVOICE", "referenceId": "43"}]}, "success": true, "message": "OK", "code": "SUCCESS"}}}