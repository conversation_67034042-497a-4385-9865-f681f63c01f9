{"data": {"account": {"id": "****************", "status": "ACTIVE", "accountType": "ORGANIZATION", "namespaceId": null, "accountProfile": {"__typename": "Profile", "id": "5a433a6c-a6c2-3ca9-ab86-55e590232d57", "profileType": "ORGANIZATION", "profileStatus": "ACTIVE"}, "profiles": {"__typename": "ProfileConnection", "edges": [{"__typename": "ProfileEdge", "node": {"__typename": "Profile", "id": "****************", "accountId": "****************", "profileType": "PERSON", "displayName": "Company - Test Offline ticket", "claimedBy": "****************", "accountRelationships": ["EMPLOYEE"], "profileStatus": "ACTIVE", "preferences": null, "personInfo": {"__typename": "PersonInfo", "contactInfo": {"__typename": "ContactInfo", "phoneNumbers": []}, "dateOfBirth": {"__typename": "DateOfBirth", "date": null}, "governmentIds": []}}, "cursor": "************************"}], "pageInfo": {"__typename": "PageInfo", "endCursor": "************************", "hasNextPage": false}}}}}