package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.apollographql.apollo.ApolloClient;
import com.google.common.io.CharStreams;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import okhttp3.OkHttpClient;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class IdentityGraphqlClientTest {

    @InjectMocks
    private WASApolloClient wasApolloClient;

    @InjectMocks
    private IdentityGraphqlClient identityGraphqlClient;

    @Mock
    private WASContextHandler wasContextHandler;

    @Mock
    private HeaderPopulator headerPopulator;

    @Mock
    private ApolloClientConfig apolloClientConfig;

    @Mock
    private OfflineTicketClient offlineTicketClient;

    private MockWebServer mockWebServer;

    @Before
    public void init() {
        Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
        Mockito.when(headerPopulator.constructAuthzHeaderFromContext()).thenReturn("authContext");
        mockWebServer = new MockWebServer();
        OkHttpClient okHttpClient = new OkHttpClient.Builder().build();

        ApolloClient apolloClient = ApolloClient.builder()
                .serverUrl(mockWebServer.url("/"))
                .okHttpClient(okHttpClient)
                .build();

        Mockito.when(apolloClientConfig.getApolloClient(ServiceName.IDENTITY)).thenReturn(apolloClient);
        Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("123")).thenReturn("realmSystemOffline");
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123");
        ReflectionTestUtils.setField(identityGraphqlClient, "wasApolloClient", wasApolloClient);
    }


    @Test
    public void test_GetRealmPersonas_3UserIds_And_Pagination() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-realm-personas-success-2-responses-pagination-true.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-realm-personas-success-2-responses.json"));
        Map<String, Persona> response = identityGraphqlClient.getRealmPersonas("****************",
                Set.of("9130360527461516", "9130360527461517", "9130360527461518", "123456789"));
        Assert.assertEquals(3, response.size());
        Persona persona1 = response.get("9130360527461516");
        Assert.assertEquals("9130360527461966", persona1.getPersonaId());
        Assert.assertEquals("<EMAIL>", persona1.getEmail().getEmailId());
        Persona persona2 = response.get("9130360527461517");
        Assert.assertEquals("9130360527461967", persona2.getPersonaId());
        Assert.assertEquals("<EMAIL>", persona2.getEmail().getEmailId());
        Persona persona3 = response.get("9130360527461518");
        Assert.assertEquals("9130360527461968", persona3.getPersonaId());
        Assert.assertEquals("<EMAIL>", persona3.getEmail().getEmailId());
    }

    @Test
    public void test_GetRealmPersonas_EmptyResponse() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-realm-personas-success-empty-response.json"));
        Map<String, Persona> response = identityGraphqlClient.getRealmPersonas("****************", Set.of("9130360527461516"));
        Assert.assertEquals(0, response.size());
    }

    @Test
    public void test_GetRealmPersonas_1UserId() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-realm-personas-success.json"));
        Map<String, Persona> response = identityGraphqlClient.getRealmPersonas("****************", Set.of("9130360527461516"));
        Assert.assertEquals(1, response.size());
        Persona persona1 = response.get("9130360527461516");
        Assert.assertEquals("9130360527461966", persona1.getPersonaId());
        Assert.assertEquals("<EMAIL>", persona1.getEmail().getEmailId());
    }


    @Test
    public void test_GetRealmPersonas_Exception() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-realm-personas-error.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getRealmPersonas("****************", Set.of("12345")));
        Assert.assertEquals(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED, workflowGeneralException.getWorkflowError());
    }

    @Test
    public void test_GetPersona() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-persona-success.json"));
        String personaId = identityGraphqlClient.getPersonaId("****************", "");
        Assert.assertEquals("9130360527461966", personaId);
    }

    @Test
    public void test_GetPersona_EmptyResponse() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-persona-success-empty-response.json"));
        String personaId = identityGraphqlClient.getPersonaId("****************", "");
        Assert.assertNull(personaId);
    }

    @Test
    public void test_GetPersona_Error() throws IOException {

        mockWebServer.enqueue(mockResponse("identityTest/get-persona-error.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getPersonaId("", ""));

        Assert.assertEquals(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED, workflowGeneralException.getWorkflowError());
    }

    @Test
    public void testAccountant_2Firms_MatchingProfiles() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-success-2-firms.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-profiles-matching-responses.json"));
        String personaId = identityGraphqlClient.getAccountantPersonaId("****************", "****************");
        Assert.assertEquals("****************", personaId);
    }

    @Test
    public void testAccountant_2Firms_NoMatchingProfiles() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-success-2-firms.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-profiles-no-matching.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getAccountantPersonaId("****************", "****************"));
        Assert.assertEquals(WorkflowError.IDENTITY_INVALID_RESPONSE_RECEIVED.getErrorMessage(),
                workflowGeneralException.getWorkflowError().getErrorMessage());
    }

    @Test
    public void testAccountant_2Firms_MatchingProfileFound_WithPagination() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-success-2-firms.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-profiles-no-matching-nextpage.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-profiles-matching-responses.json"));
        String personaId = identityGraphqlClient.getAccountantPersonaId("****************", "****************");
        Assert.assertEquals("****************", personaId);
    }

    @Test
    public void testAccountant_2Firms_ProfilesError() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-success-2-firms.json"));
        mockWebServer.enqueue(mockResponse("identityTest/get-profiles-error.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getAccountantPersonaId("****************", "****************"));
        Assert.assertEquals(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED.getErrorMessage(), workflowGeneralException.getWorkflowError().getErrorMessage());
    }

    @Test
    public void testAccountant_No_Firms() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-success-empty-firms.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getAccountantPersonaId("****************", "****************"));
        Assert.assertEquals(WorkflowError.IDENTITY_INVALID_RESPONSE_RECEIVED.getErrorMessage(),
                workflowGeneralException.getWorkflowError().getErrorMessage());
    }

    @Test
    public void testAccountant_FirmsError() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/get-firms-error.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.getAccountantPersonaId("****************", "****************"));
        Assert.assertEquals(WorkflowError.IDENTITY_GRAPHQL_API_CALL_FAILED.getErrorMessage(), workflowGeneralException.getWorkflowError().getErrorMessage());
    }

    @Test
    public void testTrustGrant_Error() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/createtrustgrant-error.json"));
        WorkflowGeneralException workflowGeneralException = Assertions.assertThrows(WorkflowGeneralException.class,
                () -> identityGraphqlClient.createTrustGrant("****************"));
        Assert.assertEquals(WorkflowError.IUS_AUTHORIZATION_DELEGATION_ERROR.getErrorMessage(), workflowGeneralException.getWorkflowError().getErrorMessage());
    }

    @Test
    public void testTrustGrant_Success() throws IOException {
        mockWebServer.enqueue(mockResponse("identityTest/createtrustgrant-success.json"));
        try {
            identityGraphqlClient.createTrustGrant("12345");
        }
        catch (Exception e){
            Assert.fail();
        }
    }

    private MockResponse mockResponse(String fileName) throws IOException {
        return new MockResponse().setChunkedBody(readFileToString(fileName), 32);
    }

    public String readFileToString(final String fileName) throws IOException {

        InputStreamReader inputStreamReader = null;
        try {
            inputStreamReader = new InputStreamReader(getClass().getClassLoader().getResourceAsStream(fileName));
            return CharStreams.toString(inputStreamReader);
        } catch (IOException e) {
            throw new IOException();
        } finally {
            if (inputStreamReader != null) {
                inputStreamReader.close();
            }
        }
    }
}
