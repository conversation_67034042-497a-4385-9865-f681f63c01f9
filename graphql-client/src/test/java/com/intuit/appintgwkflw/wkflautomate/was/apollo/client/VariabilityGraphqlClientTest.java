package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.apollographql.apollo.ApolloClient;
import com.google.common.io.CharStreams;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.ServiceProperties;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.variability.entity.graphql.GetAttributeQuery;
import okhttp3.OkHttpClient;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class VariabilityGraphqlClientTest {

  @InjectMocks private WASApolloClient wasApolloClient;

  @InjectMocks private VariabilityGraphqlClient variabilityGraphqlClient;

  @Mock private WASContextHandler wasContextHandler;

  @Mock private ApolloClientConfig apolloClientConfig;

  @Mock private OfflineTicketClient offlineTicketClient;

  private MockWebServer mockWebServer;

  @Mock
  private HeaderPopulator headerPopulator;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(variabilityGraphqlClient, "apolloClientConfig", apolloClientConfig);
    Mockito.when(apolloClientConfig.getGraphqlServiceProperties(any())).thenReturn(new ServiceProperties());
  }

  @Before
  public void init() {
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123");
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("123")).thenReturn("realmSystemOffline");

    mockWebServer = new MockWebServer();
    OkHttpClient okHttpClient = new OkHttpClient.Builder().build();

    ApolloClient apolloClient =
            ApolloClient.builder().serverUrl(mockWebServer.url("/")).okHttpClient(okHttpClient).build();

    Mockito.when(apolloClientConfig.getApolloClient(ServiceName.VARIABILITY)).thenReturn(apolloClient);
    ReflectionTestUtils.setField(variabilityGraphqlClient, "wasApolloClient", wasApolloClient);
  }

  @Test
  public void test_GetDecision_Success() throws IOException {
    mockWebServer.enqueue(mockResponse("variabilityTest/get-variability-decision-success.json"));

    List<GetAttributeQuery.VariabilityEngine_getDecisionBatch> decisionBatch =
            variabilityGraphqlClient.getDecisionBatch(List.of("isProjectWorkflowsEnabled"), new HashMap<>());

    Assert.assertNotNull(decisionBatch);
    Assert.assertNotNull(decisionBatch.get(0).value());
  }

  @Test
  public void test_GetDecision_Exception() throws IOException {
    mockWebServer.enqueue(mockResponse("variabilityTest/get-variability-decision-error.json"));
    WorkflowGeneralException workflowGeneralException =
            Assertions.assertThrows(
                    WorkflowGeneralException.class,
                    () -> variabilityGraphqlClient.getDecisionBatch(List.of("3"), new HashMap<>()));
    Assert.assertEquals(
            WorkflowError.VARIABILITY_GRAPHQL_CLIENT_FAILURE,
            workflowGeneralException.getWorkflowError());
  }

  private MockResponse mockResponse(String fileName) throws IOException {
    return new MockResponse().setChunkedBody(readFileToString(fileName), 32);
  }

  public String readFileToString(final String fileName) throws IOException {

    InputStreamReader inputStreamReader = null;
    try {
      inputStreamReader =
              new InputStreamReader(getClass().getClassLoader().getResourceAsStream(fileName));
      return CharStreams.toString(inputStreamReader);
    } catch (IOException e) {
      throw new IOException();
    } finally {
      if (inputStreamReader != null) {
        inputStreamReader.close();
      }
    }
  }
}
