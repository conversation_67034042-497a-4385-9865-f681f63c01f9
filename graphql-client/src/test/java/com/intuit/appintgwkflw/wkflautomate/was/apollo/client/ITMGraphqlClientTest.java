package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import static org.mockito.ArgumentMatchers.any;

import com.apollographql.apollo.ApolloClient;
import com.google.common.io.CharStreams;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.ServiceProperties;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceType;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import okhttp3.OkHttpClient;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class ITMGraphqlClientTest {

  @InjectMocks private WASApolloClient wasApolloClient;

  @InjectMocks private ITMGraphqlClient itmGraphqlClient;

  @Mock private WASContextHandler wasContextHandler;

  @Mock private HeaderPopulator headerPopulator;

  @Mock private ApolloClientConfig apolloClientConfig;

  @Mock private OfflineTicketClient offlineTicketClient;

  private MockWebServer mockWebServer;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(itmGraphqlClient, "apolloClientConfig", apolloClientConfig);
    Mockito.when(apolloClientConfig.getGraphqlServiceProperties(any())).thenReturn(new ServiceProperties());
  }

  @Before
  public void init() {
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Mockito.when(headerPopulator.constructAuthzHeaderFromContext()).thenReturn("authContext");
    mockWebServer = new MockWebServer();
    OkHttpClient okHttpClient = new OkHttpClient.Builder().build();

    ApolloClient apolloClient =
        ApolloClient.builder().serverUrl(mockWebServer.url("/")).okHttpClient(okHttpClient).build();

    Mockito.when(apolloClientConfig.getApolloClient(ServiceName.ITM)).thenReturn(apolloClient);
    Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob())
        .thenReturn("realmSystemOffline");
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123");
    ReflectionTestUtils.setField(itmGraphqlClient, "wasApolloClient", wasApolloClient);
  }

  @Test
  public void test_CreateITMTask_Success() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/create-itm-task-success.json"));
    TaskManagementCreateTaskMutation.Task createdITMTask =
        itmGraphqlClient.createTask(
            TaskManagement_CreateTaskInput.builder()
                .name("Test task Invoice")
                .description("This is a test task")
                .status("Inprogress")
                .dueDate(new DateTime())
                .priority(1)
                .type("QB_INVOICE_APPROVAL")
                .assignee("assignee1")
                .idempotenceId(UUID.randomUUID().toString())
                .references(
                    List.of(
                        TaskManagement_ReferenceInput.builder()
                            .referenceType(TaskManagement_ReferenceType.INVOICE)
                            .referenceId("1")
                            .build()))
                .build(),
            new HashMap<>());

    Assert.assertNotNull(createdITMTask);
    Assert.assertNotNull(createdITMTask.id());
  }

  @Test
  public void test_CreateITMTask_Exception() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/create-itm-task-error.json"));
    WorkflowGeneralException workflowGeneralException =
        Assertions.assertThrows(
            WorkflowGeneralException.class,
            () ->
                itmGraphqlClient.createTask(
                    TaskManagement_CreateTaskInput.builder()
                        .name("Test task Invoice")
                        .description("This is a test task")
                        .status("Inprogress")
                        .dueDate(new DateTime())
                        .priority(1)
                        .type("test")
                        .assignee("assignee1")
                        .idempotenceId(UUID.randomUUID().toString())
                        .references(
                            List.of(
                                TaskManagement_ReferenceInput.builder()
                                    .referenceType(TaskManagement_ReferenceType.INVOICE)
                                    .referenceId("1")
                                    .build()))
                        .build(),
                    new HashMap<>()));
    Assert.assertEquals(
        WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, workflowGeneralException.getWorkflowError());
  }

  @Test
  public void test_UpdateITMTask_Success() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/update-itm-task-success.json"));
    TaskManagementUpdateTaskMutation.Task updatedITMTask =
        itmGraphqlClient.updateTask(
            TaskManagement_UpdateTaskInput.builder()
                .id("1")
                .name("Test task Invoice 2")
                .description("Updated description")
                .assignee("assignee2")
                .build(),
            new HashMap<>());

    Assert.assertNotNull(updatedITMTask);
    Assert.assertNotNull(updatedITMTask.id());
    Assert.assertEquals("Test task Invoice 2", updatedITMTask.name());
    Assert.assertEquals("Updated description", updatedITMTask.description());
    Assert.assertEquals("assignee2", updatedITMTask.assignee());
  }

  @Test
  public void test_UpdateITMTask_Exception() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/update-itm-task-error.json"));
    WorkflowGeneralException workflowGeneralException =
        Assertions.assertThrows(
            WorkflowGeneralException.class,
            () ->
                itmGraphqlClient.updateTask(
                    TaskManagement_UpdateTaskInput.builder()
                        .id("2")
                        .name("Test task Invoice 2")
                        .description("Updated description")
                        .assignee("assignee2")
                        .build(),
                    new HashMap<>()));
    Assert.assertEquals(
        WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, workflowGeneralException.getWorkflowError());
  }

  @Test
  public void test_GetITMTask_Success() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/get-itm-task-success.json"));

    TaskManagementTaskQuery.TaskManagementTask fetchedITMTask =
        itmGraphqlClient.getTask("1", new HashMap<>());

    Assert.assertNotNull(fetchedITMTask);
    Assert.assertNotNull(fetchedITMTask.id());
  }

  @Test
  public void test_GetITMTask_Exception() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/get-itm-task-error.json"));
    WorkflowGeneralException workflowGeneralException =
        Assertions.assertThrows(
            WorkflowGeneralException.class, () -> itmGraphqlClient.getTask("3", new HashMap<>()));
    Assert.assertEquals(
        WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, workflowGeneralException.getWorkflowError());
  }

  @Test
  public void test_DeleteITMTask_Success() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/delete-itm-task-success.json"));
    boolean taskCreated = itmGraphqlClient.deleteTask("1", new HashMap<>());

    Assert.assertTrue(taskCreated);

    Mockito.verify(offlineTicketClient, Mockito.times(1)).getSystemOfflineHeadersForOfflineJob();
    Mockito.verify(apolloClientConfig, Mockito.times(1)).getApolloClient(any());
  }

  @Test
  public void test_DeleteITMTask_Exception() throws IOException {
    mockWebServer.enqueue(mockResponse("itmTest/delete-itm-task-error.json"));
    WorkflowGeneralException workflowGeneralException =
        Assertions.assertThrows(
            WorkflowGeneralException.class,
            () -> itmGraphqlClient.deleteTask("3", new HashMap<>()));
    Assert.assertEquals(
        WorkflowError.ITM_GRAPHQL_CLIENT_FAILURE, workflowGeneralException.getWorkflowError());
  }

  private MockResponse mockResponse(String fileName) throws IOException {
    return new MockResponse().setChunkedBody(readFileToString(fileName), 32);
  }

  public String readFileToString(final String fileName) throws IOException {

    InputStreamReader inputStreamReader = null;
    try {
      inputStreamReader =
          new InputStreamReader(getClass().getClassLoader().getResourceAsStream(fileName));
      return CharStreams.toString(inputStreamReader);
    } catch (IOException e) {
      throw new IOException();
    } finally {
      if (inputStreamReader != null) {
        inputStreamReader.close();
      }
    }
  }
}
