package com.intuit.appintgwkflw.wkflautomate.was.apollo.client;

import com.apollographql.apollo.ApolloClient;
import com.apollographql.apollo.api.Error;
import com.google.common.io.CharStreams;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config.ApolloClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.AuthorizationType;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.entity.GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.identity.entity.graphql.ProfilesQuery;
import com.intuit.identity.entity.graphql.type.AccountInput;
import com.intuit.identity.entity.graphql.type.AccountRelationship;
import com.intuit.identity.entity.graphql.type.ProfilesConnectionFilterInput;
import com.intuit.identity.entity.graphql.type.ProfilesRelationshipFilterInput;
import com.intuit.test.entity.graphql.AddPostalAddressMutation;
import com.intuit.test.entity.graphql.type.AddPostalAddressInput;
import com.intuit.test.entity.graphql.type.PostalAddressInput;
import okhttp3.OkHttpClient;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class WASApolloClientTest {

    @InjectMocks
    private WASApolloClient wasApolloClient;

    @Mock
    private OfflineTicketClient offlineTicketClient;

    @Mock
    private WASContextHandler wasContextHandler;

    @Mock
    private HeaderPopulator headerPopulator;

    @Mock
    private ApolloClientConfig apolloClientConfig;

    private MockWebServer mockWebServer;

    @Before
    public void init() {
        Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123");
        Mockito.when(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()).thenReturn("systemoffline");
        Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("123")).thenReturn("realmSystemOffline");
        Mockito.when(headerPopulator.constructAuthzHeaderFromContext()).thenReturn("authContext");
        mockWebServer = new MockWebServer();
        OkHttpClient okHttpClient = new OkHttpClient.Builder().build();

        ApolloClient apolloClient = ApolloClient.builder()
                .serverUrl(mockWebServer.url("/"))
                .okHttpClient(okHttpClient)
                .build();

        Mockito.when(apolloClientConfig.getApolloClient(ServiceName.IDENTITY)).thenReturn(apolloClient);
    }

    @Test
    public void testMutate_Error() throws IOException {


        AddPostalAddressMutation addPostalAddressMutation = AddPostalAddressMutation.builder()
                .request(AddPostalAddressInput.builder().accountId("123")
                        .postalAddress(PostalAddressInput.builder().build()).profileId("123")
                        .build()
                ).build();

        mockWebServer.enqueue(mockResponse("wasApolloTest/mutation-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder().mutation(addPostalAddressMutation).serviceName(ServiceName.IDENTITY).build();

        GraphqlResponse<AddPostalAddressMutation.Data> response = wasApolloClient.mutate(graphqlRequest);
        AddPostalAddressMutation.Data data = response.getResponse().getData();
        Assert.assertNotNull(data);
        Assert.assertNull(response.getError());
        Assert.assertFalse(response.isHasExceptions());

    }
    @Test
    public void testQuery() throws IOException {
        AccountInput accountInput = AccountInput.builder().id("****************").build();
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true).relationship(AccountRelationship.EMPLOYEE).build())
                .build();
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .build();


        mockWebServer.enqueue(mockResponse("wasApolloTest/success-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder().query(profilesQuery).serviceName(ServiceName.IDENTITY).build();

        GraphqlResponse<ProfilesQuery.Data> response = wasApolloClient.query(graphqlRequest);

        ProfilesQuery.Data data = response.getResponse().getData();
        Assert.assertEquals("****************", data.account().id());
        Mockito.verify(offlineTicketClient, Mockito.times(0)).getSystemOfflineHeadersForOfflineJob();
        Mockito.verify(headerPopulator, Mockito.times(1)).constructAuthzHeaderFromContext();

    }

    @Test
    public void test_ErrorResponse() throws IOException {
        AccountInput accountInput = AccountInput.builder().id("****************").build();
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true).relationship(AccountRelationship.EMPLOYEE).build())
                .build();
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .build();


        mockWebServer.enqueue(mockResponse("wasApolloTest/error-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder().query(profilesQuery).serviceName(ServiceName.IDENTITY).build();

        GraphqlResponse<ProfilesQuery.Data> response = wasApolloClient.query(graphqlRequest);

        ProfilesQuery.Data data = response.getResponse().getData();
        List<Error> error = response.getResponse().getErrors();
        Assert.assertNull(data.account());
        Assert.assertEquals("Bad Request - The provided id is not associated with an identity:****************", error.get(0).getMessage());

        Assert.assertTrue(response.isHasErrorResponse());
    }

    @Test
    public void testQuery_CorruptResponse_WithException() throws IOException {
        AccountInput accountInput = AccountInput.builder().id("****************").build();
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true).relationship(AccountRelationship.EMPLOYEE).build())
                .build();
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .build();


        mockWebServer.enqueue(mockResponse("wasApolloTest/corrupt-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder().query(profilesQuery).serviceName(ServiceName.IDENTITY).build();

        GraphqlResponse<ProfilesQuery.Data> response = wasApolloClient.query(graphqlRequest);

        Assert.assertNull(response.getResponse());
        Assert.assertTrue(response.isHasExceptions());
        Assert.assertNotNull(response.getError());


    }

    @Test
    public void test_SuccessWithsystemOfflineRealmContext() throws IOException {
        AccountInput accountInput = AccountInput.builder().id("****************").build();
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true).relationship(AccountRelationship.EMPLOYEE).build())
                .build();
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .build();


        mockWebServer.enqueue(mockResponse("wasApolloTest/success-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder().query(profilesQuery)
                .serviceName(ServiceName.IDENTITY)
                .authorizationType(AuthorizationType.SYSTEM_OFFLINE_CONTEXT_REALM)
                .build();

        GraphqlResponse<ProfilesQuery.Data> response = wasApolloClient.query(graphqlRequest);

        ProfilesQuery.Data data = response.getResponse().getData();
        Assert.assertEquals("****************", data.account().id());
        Mockito.verify(offlineTicketClient, Mockito.times(0)).getSystemOfflineHeadersForOfflineJob();
        Mockito.verify(headerPopulator, Mockito.times(0)).constructAuthzHeaderFromContext();
        Mockito.verify(offlineTicketClient, Mockito.times(1)).getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any());
    }

    @Test
    public void test_SuccessWithsystemOffline() throws IOException {
        AccountInput accountInput = AccountInput.builder().id("****************").build();
        ProfilesConnectionFilterInput profilesConnectionFilterInput = ProfilesConnectionFilterInput.builder()
                .relationshipFilter(ProfilesRelationshipFilterInput.builder()
                        .includeHidden(true).relationship(AccountRelationship.EMPLOYEE).build())
                .build();
        ProfilesQuery profilesQuery = ProfilesQuery.builder()
                .filterBy(profilesConnectionFilterInput)
                .input(accountInput)
                .build();


        mockWebServer.enqueue(mockResponse("wasApolloTest/success-response.json"));
        GraphqlRequest graphqlRequest = GraphqlRequest.builder()
                .query(profilesQuery)
                .serviceName(ServiceName.IDENTITY)
                .authorizationType(AuthorizationType.SYSTEM_OFFLINE)
                .build();

        GraphqlResponse<ProfilesQuery.Data> response = wasApolloClient.query(graphqlRequest);

        ProfilesQuery.Data data = response.getResponse().getData();
        Assert.assertEquals("****************", data.account().id());
        Mockito.verify(offlineTicketClient, Mockito.times(1)).getSystemOfflineHeadersForOfflineJob();
        Mockito.verify(headerPopulator, Mockito.times(0)).constructAuthzHeaderFromContext();
        Mockito.verify(offlineTicketClient, Mockito.times(0)).getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any());
    }

    @After
    public void tearDown() {
        try {
            mockWebServer.shutdown();
        } catch (IOException ignore) {
            //ignore
        }
    }



//    @Test
//    public void testQuery() {
//        GraphqlRequest graphqlRequest = Mockito.mock(GraphqlRequest.class);
//        Mockito.when(graphqlRequest.getQuery()).thenReturn(Mockito.any(Query.class));
//        Mockito.when(graphqlRequest.getAuthorizationType()).thenReturn(AuthorizationType.PROPAGATE);
//        Mockito.when(apolloClient.query(Mockito.any())).thenReturn(Mockito.any());
//        Mockito.when(apolloClient.query(Mockito.any()).toBuilder()).thenReturn(Mockito.any(RealApolloCall.Builder.class));
//
//    }

    private MockResponse mockResponse(String fileName) throws IOException {
        return new MockResponse().setChunkedBody(readFileToString(fileName), 32);
    }

    public String readFileToString(final String fileName) throws IOException {

        InputStreamReader inputStreamReader = null;
        try {
            inputStreamReader = new InputStreamReader(getClass().getClassLoader().getResourceAsStream(fileName));
            return CharStreams.toString(inputStreamReader);
        } catch (IOException e) {
            throw new IOException();
        } finally {
            if (inputStreamReader != null) {
                inputStreamReader.close();
            }
        }
    }

}


