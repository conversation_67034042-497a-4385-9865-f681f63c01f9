package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.config;

import com.apollographql.apollo.ApolloClient;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootConfiguration
@ContextConfiguration(classes = { OkHttpConfig.class, GraphqlEndpointConfig.class, ApolloClientConfig.class})
@EnableConfigurationProperties
@TestPropertySource(locations = "classpath:config.properties")
public class ApolloClientConfigTest {

    @Autowired
    private OkHttpConfig okHttpConfig;

    @Autowired
    private ApolloClientConfig apolloClientConfig;

    @Test
    public void testHttpConfig() {
        Assert.assertEquals(300, okHttpConfig.getConnectTimeout());
        Assert.assertEquals(400, okHttpConfig.getReadTimeout());
        Assert.assertEquals(500, okHttpConfig.getWriteTimeout());
        Assert.assertEquals(600, okHttpConfig.getCallTimeout());
        Assert.assertEquals(700, okHttpConfig.getMaxRequests());
        Assert.assertEquals(800, okHttpConfig.getMaxRequestPerHost());

    }

    @Test
    public void test_GetApolloClient_Success() {
        ApolloClient apolloClient = apolloClientConfig.getApolloClient(ServiceName.IDENTITY);
        Assert.assertNotNull(apolloClient);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void test_GetApolloClient_Error() {
        apolloClientConfig.getApolloClient(ServiceName.APP_CONNECT);
    }

    @Test
    public void test_OkHttpClientNotNull() {
        Assert.assertNotNull(apolloClientConfig.okHttpClient());
    }
}
