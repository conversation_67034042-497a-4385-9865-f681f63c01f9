package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_DEFINITION_STEP_NAME;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.EntityManagerFactory;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

public class CleanupJobDefinitionImplTest {

  private CleanupJobDefinitionImpl cleanupJobDefinitionImpl;
  @Mock
  private BatchJobConfigDetails batchJobConfigDetails;
  @Mock
  private BatchJobConfig batchJobConfig;
  @Mock
  private EntityManagerFactory entityManagerFactory;
  @Mock
  private BatchJobInitContext batchJobInitContext;
  @Mock
  private StepBuilderFactory stepBuilderFactory;
  @Mock
  private AuthDetailsService authDetailsService;
  @Mock
  private AppConnectService appConnectService;
  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;
  @Mock
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  @Mock
  private MetricLogger metricLogger;
  @Mock
  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private PlatformTransactionManager transactionManager;

  private MockedStatic<WorkflowLogger> mocked;
  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
  @Mock
  private ItemReader<DefinitionDetails> reader;
  @Mock
  private ItemProcessor<DefinitionDetails, DefinitionDetails> processor;
  @Mock
  private ItemWriter<DefinitionDetails> writer;
  @Mock
  private ThreadPoolTaskExecutor threadPoolTaskExecutor;
  @Mock
  private CleanupJobListener cleanupJobListener;
  @Mock
  private ApplicationContext applicationContext;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchJobConfigDetails);
    Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
    mocked = Mockito.mockStatic(WorkflowLogger.class);
    cleanupJobDefinitionImpl = new CleanupJobDefinitionImpl(
            batchJobConfig,
            entityManagerFactory,
            stepBuilderFactory,
            cleanupJobListener, transactionManager, threadPoolTaskExecutor, applicationContext, metricLogger);
  }

  @After
  public void deregister() {
    mocked.reset();
    mocked.close();
  }

  @Test
  public void cleanupDefinitionReaderForInitial() {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    ItemReader<DefinitionDetails> itemStreamReader = cleanupJobDefinitionImpl.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void cleanupDefinitionReader() {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(-1);
    cleanupJobDefinitionImpl.reader();
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.error(any()));
  }

  @Test
  public void cleanupDefinitionWriter() {
    ItemWriter<DefinitionDetails> itemWriter = cleanupJobDefinitionImpl.writer();
    Assert.assertNotNull(itemWriter);
  }

  @Test
  public void cleanupWorkflowDefinitionStep() {
    StepBuilder stepBuilder = Mockito.mock(StepBuilder.class);
    AbstractTaskletStepBuilder abstractTaskletStepBuilder = Mockito.mock(
        AbstractTaskletStepBuilder.class);

    SimpleStepBuilder<DefinitionDetails, DefinitionDetails> simpleStepBuilder = Mockito.mock(
        SimpleStepBuilder.class);
    TaskletStep taskletStep = Mockito.mock(TaskletStep.class);
    Mockito.when(batchJobConfig.getChunkSize()).thenReturn(5);
    Mockito.when(stepBuilderFactory.get(CLEANUP_DEFINITION_STEP_NAME)).thenReturn(stepBuilder);
    Mockito.when(stepBuilder.transactionManager(transactionManager)).thenReturn(stepBuilder);
    Mockito.when(
            stepBuilder.<DefinitionDetails, DefinitionDetails>chunk(batchJobConfig.getChunkSize()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.reader(any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.writer(any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.listener((CleanupJobListener) any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(
            simpleStepBuilder.processor((ItemProcessor<DefinitionDetails, DefinitionDetails>) any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.taskExecutor(any()))
        .thenReturn(abstractTaskletStepBuilder);
    Mockito.when(abstractTaskletStepBuilder.build()).thenReturn(taskletStep);
    Step step = cleanupJobDefinitionImpl.createStep(reader, processor, writer);

    Assert.assertNotNull(step);
  }

  @Test
  public void testGetName() {
    String res = cleanupJobDefinitionImpl.getName().getName();
    Assert.assertNotNull(res);
  }

  @Test
  public void testGetPriority() {
    Integer priority = cleanupJobDefinitionImpl.getPriority();
    Assert.assertNotNull(priority);
  }

}