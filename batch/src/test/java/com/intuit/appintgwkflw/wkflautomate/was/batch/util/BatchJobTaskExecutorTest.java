package com.intuit.appintgwkflw.wkflautomate.was.batch.util;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.util.BatchJobTaskExecutor;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

public class BatchJobTaskExecutorTest {

  @InjectMocks
  private BatchJobTaskExecutor batchJobTaskExecutor;
  @Mock
  private BatchJobConfig batchConfigMock;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void cleanupThreadPoolExecutor() {
    Mockito.when(batchConfigMock.getThreadPoolSize()).thenReturn(5);
    ThreadPoolTaskExecutor taskExecutor = batchJobTaskExecutor.batchThreadPoolTaskExecutor();

    Assert.assertEquals(taskExecutor.getCorePoolSize(), 5);
    Assert.assertEquals(taskExecutor.getThreadNamePrefix(),
        BatchJobConstants.BATCH_TASK_EXECUTOR_NAME_PREFIX);
  }

}
