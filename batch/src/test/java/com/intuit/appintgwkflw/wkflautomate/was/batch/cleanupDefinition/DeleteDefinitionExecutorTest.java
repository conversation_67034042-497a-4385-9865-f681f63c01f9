package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeleteEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;
import com.intuit.async.execution.request.State;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import com.intuit.v4.common.RecurrenceRule;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;

public class DeleteDefinitionExecutorTest {

  @InjectMocks
  private DeleteDefinitionExecutor deleteDefinitionExecutor;
  @Mock
  private BatchJobConfig batchJobConfig;
  @Mock
  private BatchJobInitContext batchJobInitContext;
  @Mock
  private MetricLogger metricLogger;
  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  @Mock
  private AppConnectService appConnectService;
  @Mock
  private AuthDetailsService authDetailsService;
  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;
  @Mock
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  @Mock
  private EventScheduleHelper eventScheduleHelper;
  @Mock
  private BatchJobConfigDetails batchJobConfigDetails;
  @Mock private SchedulingService schedulingService;
  private MockedStatic<WorkflowLogger> mocked;
  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
  private DefinitionDetails definitionDetails;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchJobConfigDetails);
    Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
    mocked = Mockito.mockStatic(WorkflowLogger.class);
    definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DELETE);
    definitionDetails.setOwnerId(123L);
    definitionDetails.setWorkflowId("12345");
    UpdateEventScheduleTask updateStatusEventSchedulerTask =
        Mockito.mock(UpdateEventScheduleTask.class);
    DeleteEventSchedulingTask deleteEventSchedulingTask =
        Mockito.mock(DeleteEventSchedulingTask.class);
    Mockito.when(updateStatusEventSchedulerTask.execute(any())).thenReturn(new State());
    Mockito.when(deleteEventSchedulingTask.execute(any())).thenReturn(new State());
    Mockito.when(eventScheduleHelper.prepareScheduleStatusUpdateTask(any(), any(), any()))
        .thenReturn(updateStatusEventSchedulerTask);
    Mockito.when(eventScheduleHelper.prepareSchedulingDeleteTask(Mockito.any(), Mockito.any())).thenReturn(deleteEventSchedulingTask);
  }

  @After
  public void deregister() {
    mocked.reset();
    mocked.close();
  }

  @Test
  public void cleanupDefinitionProcessor_SingleDefinitionDelete_success() throws Exception {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("test");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.SINGLE);

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetails);
    deleteDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .deleteByDefinitionIdOrParentId(any(), any());

  }

  @Test
  public void cleanupDefinitionProcessor_SingleDefinitionDeleteForOldScheduledActions_success() throws Exception {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customScheduledActions");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.setOfferingId(123123L);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.SINGLE);
    List<String> scheduleIds = new ArrayList<>();
    scheduleIds.add("id1");

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
            .thenReturn(authDetails);

    Mockito.when(eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(scheduleIds);
    deleteDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .deleteByDefinitionIdOrParentId(any(), any());
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).prepareScheduleStatusUpdateTask(any(), any(), any());
  }

  @Test
  public void cleanupDefinitionProcessor_SingleDefinitionDeleteForNewScheduledActions_success_FFON() throws Exception {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customScheduledActions");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.SINGLE);
    definitionDetails.setOwnerId(3726537162L);
    List<String> scheduleIds = new ArrayList<>();

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
            .thenReturn(authDetails);
    Mockito.when(schedulingService.isEnabled((DefinitionDetails) any(), any())).thenReturn(true);
    Mockito.when(eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(scheduleIds);
    deleteDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .deleteByDefinitionIdOrParentId(any(), any());
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).prepareSchedulingDeleteTask(any(), any());
  }

  @Test
  public void cleanupDefinitionProcessor_SingleDefinitionDeleteForNewScheduledActions_success_FFOFF() throws Exception {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customScheduledActions");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.SINGLE);
    definitionDetails.setOwnerId(3726537162L);
    List<String> scheduleIds = new ArrayList<>();

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
            .thenReturn(authDetails);
    Mockito.when(schedulingService.isEnabled((DefinitionDetails) any(), any())).thenReturn(false);
    Mockito.when(eventScheduleHelper.getScheduleIdsForDefinition(any())).thenReturn(scheduleIds);
    deleteDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .deleteByDefinitionIdOrParentId(any(), any());
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).prepareScheduleStatusUpdateTask(any(), any(), any());
  }

  @Test
  public void cleanupDefinitionProcessor_UserDefinitionDelete_success() throws Exception {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("test");
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.USER);

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetails);

    DeploymentResponse deploymentResponse = new DeploymentResponse();
    deploymentResponse.setDeploymentId("d11");
    Mockito.when(
            bpmnEngineDefinitionServiceRest.getDeploymentDetails(definitionDetails.getDefinitionId()))
        .thenReturn(
            WASHttpResponse.<DeploymentResponse>builder().status(
                    HttpStatus.NO_CONTENT).isSuccess2xx(true)
                .response(deploymentResponse)
                .build());
    Mockito.doNothing().when(bpmnEngineDefinitionServiceRest).deleteDeployment(Mockito.any());
    deleteDefinitionExecutor.handle(definitionDetails);
    Mockito.verify(dataStoreDeleteTaskService, Mockito.times(1)).deleteDefinitions(any());
    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
            .deleteByDefinitionIdOrParentId(any(), any());

  }

  @Test
  public void cleanupDefinitionProcessorDelete_Exception() throws Exception {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);
    definitionDetails.setModifiedDate(timestamp);

    TemplateDetails templateDetails = new TemplateDetails();
    definitionDetails.setTemplateDetails(templateDetails);
    definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.USER);

    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetails);

    DeploymentResponse deploymentResponse = new DeploymentResponse();
    deploymentResponse.setDeploymentId("d11");
    Mockito.when(
            bpmnEngineDefinitionServiceRest.getDeploymentDetails(definitionDetails.getDefinitionId()))
        .thenReturn(
            WASHttpResponse.<DeploymentResponse>builder().status(
                    HttpStatus.NO_CONTENT).isSuccess2xx(false)
                .response(deploymentResponse)
                .build());
    Mockito.doNothing().when(bpmnEngineDefinitionServiceRest).deleteDeployment(Mockito.any());
    deleteDefinitionExecutor.handle(definitionDetails);

    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Assert.assertNotEquals(definitionDetails.getModifiedDate(), timestamp);
  }

  @Test
  public void cleanupDefinitionProcessor_checkEligibility_nonZeroError_Exception() {
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ACTIVE)).thenReturn(5l);
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ERROR)).thenReturn(0l);

    deleteDefinitionExecutor.handle(definitionDetails);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
  }

  @Test
  public void cleanupDefinitionProcessor_checkEligibility_whenCleanActiveProcessIsEnabled() {
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);

    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ACTIVE)).thenReturn(5l);
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ERROR)).thenReturn(0l);

    deleteDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(processDetailsRepository, Mockito.times(0))
        .countByDefinitionDetailsAndProcessStatus(definitionDetails, ProcessStatus.ERROR);
    Mockito.verify(processDetailsRepository, Mockito.times(1))
        .countByDefinitionDetailsAndProcessStatus(definitionDetails, ProcessStatus.ACTIVE);
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(BatchCleanupStatusType.DELETE_DEFINITION,
        deleteDefinitionExecutor.getName());
  }
}
