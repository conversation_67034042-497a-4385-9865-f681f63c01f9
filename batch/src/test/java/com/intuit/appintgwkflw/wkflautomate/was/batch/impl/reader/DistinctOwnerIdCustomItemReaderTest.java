package com.intuit.appintgwkflw.wkflautomate.was.batch.impl.reader;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.data.domain.PageRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class DistinctOwnerIdCustomItemReaderTest {

    @Mock
    TemplateDetailsRepository templateDetailsRepository;

    @Mock
    DefinitionDetailsRepository definitionDetailsRepository;

    @Mock
    BatchJobConfig batchJobConfig;

    @Test
    public void test_TemplateDetailsMissing() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, null,batchJobConfig);
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("templateDetailsRepository must not be null", e.getMessage());
    }

    @Test
    public void test_DefinitionDetailsMissing() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(null, templateDetailsRepository,batchJobConfig);
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("definitionsDetailsRepository must not be null", e.getMessage());
    }

    @Test
    public void test_TemplatesIdsNull() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification")))
        .thenReturn(null);
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.init();
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("templateIds must not be empty", e.getMessage());
    }

    @Test
    public void test_TemplatesIdsEmpty() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(List.of());
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.init();
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("templateIds must not be empty", e.getMessage());
    }

    @Test
    public void test_starOwnerIdGreaterThanZero() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(List.of("1", "2"));
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.empty());
        distinctOwnerIdCustomItemReader.setItemLimit(10);
        distinctOwnerIdCustomItemReader.init();
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("startOwnerId must be greater than 0", e.getMessage());
    }

    @Test
    public void test_itemLimitNotNull() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(List.of("1", "2"));
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.setItemLimit(null);
        distinctOwnerIdCustomItemReader.init();
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("itemLimit must not be null", e.getMessage());
    }

    @Test
    public void test_itemLimitGreaterThanZero() {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(List.of("1", "2"));
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.setItemLimit(0);
        distinctOwnerIdCustomItemReader.init();
        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("itemLimit must be greater than 0", e.getMessage());
    }

    @Test
    public void test_read() throws Exception {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        List<String> templateIds = List.of("1", "2");
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(templateIds);
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.setItemLimit(2);
        distinctOwnerIdCustomItemReader.init();
        distinctOwnerIdCustomItemReader.afterPropertiesSet();
        ArgumentCaptor<PageRequest> pageRequestArgumentCaptor = ArgumentCaptor.forClass(PageRequest.class);
        Mockito.when(definitionDetailsRepository.findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(
                Mockito.eq(19L),
                Mockito.eq(templateIds), pageRequestArgumentCaptor.capture())).thenReturn(List.of(20L, 23L));
        Assert.assertEquals(20L, distinctOwnerIdCustomItemReader.read().longValue());
        Assert.assertEquals(23L, distinctOwnerIdCustomItemReader.read().longValue());
        Assert.assertEquals(0, pageRequestArgumentCaptor.getValue().getPageNumber());
        Assert.assertEquals(2, pageRequestArgumentCaptor.getValue().getPageSize());
        Mockito.when(definitionDetailsRepository.findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(
                Mockito.eq(23L),
                Mockito.eq(templateIds), pageRequestArgumentCaptor.capture())).thenReturn(List.of(28L));
        Assert.assertEquals(28L, distinctOwnerIdCustomItemReader.read().longValue());
        Assert.assertNull(distinctOwnerIdCustomItemReader.read());
        Mockito.verify(definitionDetailsRepository, Mockito.times(2)).findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(
                Mockito.anyLong(),
                Mockito.eq(templateIds), Mockito.any());

    }

    @Test
    public void test_open_update_close() throws Exception {
        DistinctOwnerIdCustomItemReader distinctOwnerIdCustomItemReader = new DistinctOwnerIdCustomItemReader(definitionDetailsRepository, templateDetailsRepository,batchJobConfig);
        List<String> templateIds = List.of("1", "2");
        Map<BatchJobType, BatchJobConfigDetails> map = new HashMap<>();
        BatchJobConfigDetails value = new BatchJobConfigDetails();
        value.setWorkflows("customNotification");
        map.put(BatchJobType.CACHE_WARMUP,value);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(map);
        Mockito.when(templateDetailsRepository.findCustomTemplateIds(List.of("customNotification"))).thenReturn(templateIds);
        Mockito.when(definitionDetailsRepository.findSmallestPositiveOwnerId()).thenReturn(Optional.of(20L));
        distinctOwnerIdCustomItemReader.setItemLimit(2);
        distinctOwnerIdCustomItemReader.init();
        distinctOwnerIdCustomItemReader.setSaveState(true);
        distinctOwnerIdCustomItemReader.afterPropertiesSet();
        ExecutionContext executionContext = new ExecutionContext();
        executionContext.putLong(distinctOwnerIdCustomItemReader.getExecutionContextKey("read.start.ownerId"), 5);

        distinctOwnerIdCustomItemReader.open(executionContext);
        Mockito.when(definitionDetailsRepository.findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(
                Mockito.eq(5L),
                Mockito.eq(templateIds), Mockito.any())).thenReturn(List.of(28L));

        Assert.assertEquals(28L, distinctOwnerIdCustomItemReader.read().longValue());

        Mockito.verify(definitionDetailsRepository, Mockito.times(1)).findDistinctOwnerIdsForTemplateIdsGreaterThanAndLimit(
                Mockito.anyLong(),
                Mockito.eq(templateIds), Mockito.any());

        distinctOwnerIdCustomItemReader.update(executionContext);
        Assert.assertEquals(28L, executionContext.getLong(distinctOwnerIdCustomItemReader.getExecutionContextKey("read.start.ownerId")));

        distinctOwnerIdCustomItemReader.close();

        Exception e = Assertions.assertThrows(IllegalArgumentException.class, distinctOwnerIdCustomItemReader::afterPropertiesSet);
        Assert.assertEquals("templateIds must not be empty", e.getMessage());

    }

}
