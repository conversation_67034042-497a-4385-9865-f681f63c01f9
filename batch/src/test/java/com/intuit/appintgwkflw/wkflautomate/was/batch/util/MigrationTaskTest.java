package com.intuit.appintgwkflw.wkflautomate.was.batch.util;

import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;


public class MigrationTaskTest {

    @Mock
    private MigrationServiceHelper migrationServiceHelper;
    @Mock
    private DefinitionDetails mockDefinitionDetails;

    @Mock
    private Authorization mockAuthorization;

    @Mock
    private BatchJobInitContext mockBatchJobInitContext;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testMigrationTask(){
        State state = new State();
        state.addValue(WorkflowConstants.TEMPLATE_ID, "testId");
        Definition def = new Definition();
        Mockito.when(migrationServiceHelper.migrateDefinition(mockDefinitionDetails, mockAuthorization, "testId")).thenReturn(def);
        Mockito.when(mockBatchJobInitContext.generateAuthorization(mockDefinitionDetails)).thenReturn(mockAuthorization);
        MigrationTask migrationTask = new MigrationTask(migrationServiceHelper, mockDefinitionDetails, mockBatchJobInitContext);
        migrationTask.execute(state);
        Assert.assertNotNull(state.getValue(WorkflowConstants.MIGRATED_DEFINITION));
    }

    @Test
    public void testMigrationTaskFailure(){
        State state = new State();
        state.addValue(WorkflowConstants.TEMPLATE_ID, "testId");
        Mockito.when(migrationServiceHelper.migrateDefinition(mockDefinitionDetails, mockAuthorization, "testId")).thenThrow(IndexOutOfBoundsException.class);
        Mockito.when(mockBatchJobInitContext.generateAuthorization(mockDefinitionDetails)).thenReturn(mockAuthorization);
        MigrationTask migrationTask = new MigrationTask(migrationServiceHelper, mockDefinitionDetails, mockBatchJobInitContext);
        migrationTask.execute(state);
    }

}
