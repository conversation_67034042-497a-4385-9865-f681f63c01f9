package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_BATCH_THREAD_POOL_TASK_EXECUTOR;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_STEP_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ThreadPool;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.v4.workflows.Definition;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.persistence.EntityManagerFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;


public class UserToSingleDefinitionMigrationImplTest {

  private UserToSingleDefinitionMigrationImpl userToSingleDefinitionMigration;

  @Mock
  private BatchJobConfigDetails batchJobConfigDetails;
  @Mock
  private BatchJobConfig batchConfig;
  @Mock
  private EntityManagerFactory entityManagerFactory;
  @Mock
  private BatchJobInitContext batchJobInitContext;
  @Mock
  private StepBuilderFactory stepBuilderFactory;
  @Mock
  private TemplateDetailsRepository templateDetailsRepository;
  @Mock
  private TemplateDetails templateDetails;
  @Mock
  private Optional<TemplateDetails> templateDetailsOptional;
    @Mock
    private ApplicationContext applicationContext;
  @Mock
  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private PlatformTransactionManager transactionManager;
  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
  @Mock
  private MigrationServiceHelper migrationServiceHelper;
  @Mock
  private Definition mockDefinition;
  @Mock
  private ItemReader<DefinitionDetails> reader;
  @Mock
  private ItemProcessor<DefinitionDetails, DefinitionDetails> processor;
  @Mock
  private ItemWriter<DefinitionDetails> writer;
  @Mock
  private MetricLogger metricLogger;

  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;

  private ThreadPoolTaskExecutor threadPoolTaskExecutor;


  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.USER_TO_SINGLE_DEFINITION_MIGRATION, batchJobConfigDetails);
    Mockito.when(batchConfig.getStepConfig()).thenReturn(jobConfig);

    threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
    threadPoolTaskExecutor.setCorePoolSize(55);

    userToSingleDefinitionMigration = new UserToSingleDefinitionMigrationImpl(
        entityManagerFactory, templateDetailsRepository, batchConfig, batchJobInitContext,
            threadPoolTaskExecutor, migrationServiceHelper, stepBuilderFactory,
            transactionManager, definitionDetailsRepository, applicationContext, metricLogger);
  }


  @Test
  public void migrateDefinitionReaderForInitial() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'14'");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionReaderForAllVersion() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionReaderForDefinitionTypeUser() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(batchJobConfigDetails.getDefinitionType()).thenReturn(null);
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionReaderForDefinitionTypeSingle() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(batchJobConfigDetails.getDefinitionType()).thenReturn("SINGLE");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionReaderError() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'16'");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test(expected = IndexOutOfBoundsException.class)
  public void migrateDefinitionReaderException() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(batchJobConfigDetails.getTemplateVersion())
        .thenThrow(IndexOutOfBoundsException.class);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getVersion()).thenReturn(15);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'16'");

    ItemReader<DefinitionDetails> itemStreamReader = userToSingleDefinitionMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionWriter() {
    ItemWriter<DefinitionDetails> itemWriter = userToSingleDefinitionMigration.writer();
    Assert.assertNotNull(itemWriter);
  }

  @Test
  public void migrateDefinitionProcessor() throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    Mockito.when(templateDetails.getVersion()).thenReturn(20);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("xyz").version(19).build());
    AuthDetailsDto authDetails = new AuthDetailsDto();
    authDetails.setAuthDetailsId("11");
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetails);

    Mockito.when(definitionDetailsRepository.findByDefinitionId(Mockito.any())).thenReturn(Optional.of(definitionDetails));
    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(mockDefinition);

    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = userToSingleDefinitionMigration.processor();
    DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
    Assert.assertNotNull(definitionDetails1);

  }

  @Test
  public void migrateDefinitionProcessorException() throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");

    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_OFFLINE_TICKET));

    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(mockDefinition);

    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = userToSingleDefinitionMigration.processor();
    DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
    Assert.assertNull(definitionDetails1);
  }

  @Test
  public void migrateDefinitionProcessorElseException() throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");

    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenThrow(
            new WorkflowGeneralException(WorkflowError.DEFINITION_MIGRATION_FAILED));

    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(mockDefinition);

    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = userToSingleDefinitionMigration.processor();
    DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
    Assert.assertNull(definitionDetails1);
  }

  @Test
  public void migrateDefinitionProcessorException2() throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
    Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
        .thenReturn(templateDetailsOptional);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");

    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenThrow(IndexOutOfBoundsException.class);

    Mockito.when(
            migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(mockDefinition);

    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = userToSingleDefinitionMigration.processor();
    DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
    Assert.assertNull(definitionDetails1);
  }


  @Test
  public void migrateWorkflowDefinitionStep() {
    StepBuilder stepBuilder = Mockito.mock(StepBuilder.class);
    AbstractTaskletStepBuilder abstractTaskletStepBuilder = Mockito.mock(
        AbstractTaskletStepBuilder.class);

    SimpleStepBuilder<DefinitionDetails, DefinitionDetails> simpleStepBuilder = Mockito.mock(
        SimpleStepBuilder.class);
    TaskletStep taskletStep = Mockito.mock(TaskletStep.class);
    Mockito.when(batchConfig.getChunkSize()).thenReturn(5);
    Mockito.when(stepBuilderFactory.get(USER_TO_SINGLE_DEFINITION_STEP_NAME))
        .thenReturn(stepBuilder);
    Mockito.when(stepBuilder.transactionManager(transactionManager)).thenReturn(stepBuilder);
    Mockito.when(
            stepBuilder.<DefinitionDetails, DefinitionDetails>chunk(batchConfig.getChunkSize()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.reader(Mockito.any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.writer(Mockito.any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.listener((CleanupJobListener) Mockito.any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.processor(
            (ItemProcessor<DefinitionDetails, DefinitionDetails>) Mockito.any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.taskExecutor(Mockito.any()))
        .thenReturn(abstractTaskletStepBuilder);
    Mockito.when(abstractTaskletStepBuilder.build()).thenReturn(taskletStep);
    Step step = userToSingleDefinitionMigration.createStep(reader, processor, writer);

    Assert.assertNotNull(step);
  }

  @Test
  public void testGetName() {
    String res = userToSingleDefinitionMigration.getName().getName();
    Assert.assertNotNull(res);
  }

  @Test
  public void testGetPriority() {
    Integer priority = userToSingleDefinitionMigration.getPriority();
    Assert.assertNotNull(priority);
  }


  @Test
  public void testGetThreadPoolTaskExecutorWithDefaultConfigs() {
    ThreadPoolTaskExecutor threadPoolTaskExecutor = userToSingleDefinitionMigration.getThreadPoolTaskExecutor();
    Assert.assertNotNull(threadPoolTaskExecutor);
    Assert.assertEquals(55, threadPoolTaskExecutor.getCorePoolSize());
  }

  @Test
  public void testGetThreadPoolTaskExecutorWithCustomConfigs() {
    ThreadPool threadPool = new ThreadPool();
    threadPool.setSharedMinThreads(23);

    BatchJobConfigDetails batchJobConfigDetails = new BatchJobConfigDetails();
    batchJobConfigDetails.setThreadPool(threadPool);

    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.USER_TO_SINGLE_DEFINITION_MIGRATION, batchJobConfigDetails);
    Mockito.when(batchConfig.getStepConfig()).thenReturn(jobConfig);
    Mockito.when(applicationContext.getBean(CUSTOM_BATCH_THREAD_POOL_TASK_EXECUTOR)).thenReturn(
        new ThreadPoolTaskExecutor());

    ThreadPoolTaskExecutor threadPoolTaskExecutor = userToSingleDefinitionMigration.getThreadPoolTaskExecutor();
    Assert.assertNotNull(threadPoolTaskExecutor);
    Assert.assertEquals(23, threadPoolTaskExecutor.getCorePoolSize());
  }

  @Test
  public void testGetBatchSizeWithDefaultConfigs() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Assert.assertEquals(10, userToSingleDefinitionMigration.getBatchSize());
  }

  @Test
  public void testGetBatchSizeWithCustomConfigs() {
    Mockito.when(batchJobConfigDetails.getBatchSize()).thenReturn(30);
    Assert.assertEquals(30, userToSingleDefinitionMigration.getBatchSize());
  }

  @Test
  public void testGetChunkSizeWithDefaultConfigs() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(50);
    Assert.assertEquals(50, userToSingleDefinitionMigration.getBatchSize());
  }

  @Test
  public void testGetChunkSizeWithCustomConfigs() {
    Mockito.when(batchJobConfigDetails.getBatchSize()).thenReturn(100);
    Assert.assertEquals(100, userToSingleDefinitionMigration.getBatchSize());
  }

}
