package com.intuit.appintgwkflw.wkflautomate.was.batch;

import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.CleanupJobDefinitionImpl;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.job.builder.SimpleJobBuilder;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class BatchJobInitTest {

  @InjectMocks
  private BatchJobInit batchJobInit;
  @Mock
  private ApplicationContext applicationContext;
  @Mock
  private List<BatchService<?>> batchServicesList;
  @Mock
  private CleanupJobDefinitionImpl cleanupJobDeleteDefinition;
  @Mock
  private BatchJobConfig batchConfigMock;
  @Mock
  private JobBuilderFactory myJobBuilderFactory;
  @Mock
  private BatchJobConfigDetails batchConfigDetailsMock;
  @Mock
  private Step batchStep;
  @Mock
  private ListIterator iterator;

  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;


  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    MockitoAnnotations.initMocks(this.myJobBuilderFactory);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchConfigDetailsMock);
    Mockito.when(batchConfigMock.getJobName()).thenReturn("jobName");
    batchServicesList.add(cleanupJobDeleteDefinition);
  }

  @Test
  public void cleanupWorkflowDefinitions() {
    JobBuilder jobBuilder = Mockito.mock(JobBuilder.class);
    SimpleJobBuilder simpleJobBuilder = Mockito.mock(SimpleJobBuilder.class);
    BatchService batchServiceMock = Mockito.mock(BatchService.class);
    Job job = Mockito.mock(Job.class);
    Mockito.when(batchConfigMock.getJobName()).thenReturn("BatchJobName");
    Mockito.when(myJobBuilderFactory.get("BatchJobName")).thenReturn(jobBuilder);
    Mockito.when(jobBuilder.incrementer(Mockito.any())).thenReturn(jobBuilder);
    Mockito.when(jobBuilder.start(Mockito.any(Step.class))).thenReturn(simpleJobBuilder);
    Mockito.when(simpleJobBuilder.build()).thenReturn(job);
    Mockito.when(simpleJobBuilder.next(Mockito.any(Step.class))).thenReturn(simpleJobBuilder);
    Mockito.when(batchServicesList.listIterator()).thenReturn(iterator);
    Mockito.when(iterator.next()).thenReturn(batchServiceMock);
    Mockito.when(iterator.hasNext()).thenReturn(true, false);
    Mockito.when(batchServiceMock.getName()).thenReturn(BatchJobType.CLEANUP_DEFINITION);
    Mockito.when(applicationContext.getBean(Mockito.anyString())).thenReturn(batchStep);
    Job job1 = batchJobInit.workflowBatchJob();

    Assert.assertNotNull(job1);
  }

}