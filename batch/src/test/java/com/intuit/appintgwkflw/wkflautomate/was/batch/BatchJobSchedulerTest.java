package com.intuit.appintgwkflw.wkflautomate.was.batch;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DeploymentUtil;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobExecutionAlreadyRunningException;
import org.springframework.batch.core.repository.JobRepository;
import uk.org.webcompere.systemstubs.rules.EnvironmentVariablesRule;

import java.util.HashMap;
import java.util.Map;

public class BatchJobSchedulerTest {

  @InjectMocks
  private BatchJobScheduler batchJobScheduler;
  @Mock
  private JobLauncher jobLauncher;
  @Mock
  private Job scheduleBatchJob;
  @Mock
  private JobRepository jobRepository;
  @Mock
  private BatchJobConfigDetails batchCleanupConfig;
  @Mock
  private WASContextHandler contextHandler;
  @Mock
  private MetricLogger metricLogger;
 @Mock
 private BatchJobConfig batchJobConfig;
  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;

  @Rule
  public EnvironmentVariablesRule environmentVariablesRule = new EnvironmentVariablesRule();

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchCleanupConfig);
    Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
  }

  @Test
  public void testScheduleRefreshJob_Success() throws Exception {
    environmentVariablesRule.set(DeploymentUtil.APP_NAME, "wkflatmnsvc");
    JobExecution jobExecution = Mockito.mock(JobExecution.class);
    Mockito.when(jobExecution.getStatus()).thenReturn(BatchStatus.COMPLETED);
    Mockito.when(jobLauncher.run(eq(scheduleBatchJob), any(JobParameters.class)))
        .thenReturn(jobExecution);
    batchJobScheduler.scheduleBatchJob();
    Mockito.verify(jobLauncher).run(eq(scheduleBatchJob), any(JobParameters.class));
  }

  @Test
  public void testScheduleRefreshJob_AlreadyRunning() {
    JobExecution jobExecution = new JobExecution(123L);
    jobExecution.setStatus(BatchStatus.COMPLETED);
    Mockito.when(scheduleBatchJob.getName()).thenReturn("123");
    Mockito.when(jobRepository.getLastJobExecution(anyString(), any(JobParameters.class)))
        .thenReturn(jobExecution);
    batchJobScheduler.scheduleBatchJob();
    Mockito.verifyZeroInteractions(jobLauncher);
  }

  @Test
  public void testScheduleRefreshJob_Exception() throws Exception {
    JobExecution jobExecution = new JobExecution(123L);
    jobExecution.setStatus(BatchStatus.COMPLETED);
    Mockito.when(scheduleBatchJob.getName()).thenReturn("123");
    Mockito.when(jobRepository.getLastJobExecution(anyString(), any(JobParameters.class)))
        .thenReturn(null);
    Mockito.doThrow(
            new JobExecutionAlreadyRunningException("")).
        when(jobLauncher).run(any(Job.class), any(JobParameters.class));
    batchJobScheduler.scheduleBatchJob();
    Mockito.verify(jobRepository).getLastJobExecution(anyString(), any());
  }
}
