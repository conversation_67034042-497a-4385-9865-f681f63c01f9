package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.async.execution.request.State;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class DisableDefinitionExecutorTest {

  private DefinitionDetails definitionDetails;

  private AuthDetails authDetails;
  private AuthDetailsDto authDetailsDto;
  @InjectMocks
  private DisableDefinitionExecutor disableDefinitionExecutor;
  @Mock
  private BatchJobConfig batchJobConfig;
  @Mock
  private BatchJobInitContext batchJobInitContext;
  @Mock
  private MetricLogger metricLogger;
  @Mock
  private ProcessDetailsRepository processDetailsRepository;
  @Mock
  private AppConnectService appConnectService;
  @Mock
  private AuthDetailsService authDetailsService;
  @Mock
  private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock
  private BatchJobConfigDetails batchJobConfigDetails;
  @Mock
  private EventScheduleHelper eventScheduleHelper;
  @Mock
  private SchedulingService schedulingService;
  private MockedStatic<WorkflowLogger> mocked;
  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
  private UpdateEventScheduleTask updateStatusEventSchedulerTask;
  private UpdateEventSchedulingTask updateEventSchedulingTask;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchJobConfigDetails);
    Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
    mocked = Mockito.mockStatic(WorkflowLogger.class);
    definitionDetails = new DefinitionDetails();
    authDetails = new AuthDetails();
    authDetailsDto = new AuthDetailsDto();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DISABLE);
    definitionDetails.setTemplateDetails(
        TemplateDetails.builder().templateName("workflow").build());
    definitionDetails.setOwnerId(123L);
    definitionDetails.setWorkflowId("12345");
    authDetails.setAuthDetailsId("11");
    authDetails.setSubscriptionId("123");
    updateStatusEventSchedulerTask = Mockito.mock(UpdateEventScheduleTask.class);
    Mockito.when(updateStatusEventSchedulerTask.execute(Mockito.any()))
        .thenReturn(Mockito.mock(State.class));
    updateEventSchedulingTask = Mockito.mock(UpdateEventSchedulingTask.class);
    Mockito.when(updateEventSchedulingTask.execute(Mockito.any()))
        .thenReturn(Mockito.mock(State.class));
    Mockito.when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(false);
  }

  @After
  public void deregister() {
    mocked.reset();
    mocked.close();
  }

  @Test
  public void cleanupDefinitionProcessorDisable_success() {
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);
    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenReturn(Mockito.mock(
            AppConnectSaveWorkflowResponse.class));

    disableDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
        .updateInternalStatus(any(), any());
  }

  @Test
  public void cleanupDefinitionProcessorDisable_Exception() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenReturn(Mockito.mock(
            AppConnectSaveWorkflowResponse.class));

    disableDefinitionExecutor.handle(definitionDetails);

    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Assert.assertTrue(definitionDetails.getModifiedDate().after(timestamp));
  }

  @Test
  public void cleanupDefinitionProcessorDisable_IAC404Exception() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);

    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean())).thenThrow(new WorkflowGeneralException(WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, "111", WorkflowConstants.RESOURCE_NOT_FOUND));

    disableDefinitionExecutor.handle(definitionDetails);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logInfo(
        "step=definitionDisableFailure Workflow does not exist in app connect. Updating status in DB definitionId=%s",
        definitionDetails.getDefinitionId()));
    Assert.assertEquals(definitionDetails.getModifiedDate(), timestamp);
  }

  @Test
  public void cleanupDefinitionProcessor_deactivatingException() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);

    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);

    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL));

    disableDefinitionExecutor.handle(definitionDetails);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Assert.assertTrue(definitionDetails.getModifiedDate().after(timestamp));

  }

  @Test
  public void cleanupDefinitionProcessor_InvalidAuthException() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);
    definitionDetails.setModifiedDate(timestamp);
    authDetails = new AuthDetails();
    authDetailsDto = new AuthDetailsDto();

    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenReturn(Mockito.mock(
            AppConnectSaveWorkflowResponse.class));

    disableDefinitionExecutor.handle(definitionDetails);

    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
    Assert.assertTrue(definitionDetails.getModifiedDate().after(timestamp));
  }

  @Test
  public void cleanupDefinitionProcessor_checkEligibility_nonZeroError_Exception() {
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ACTIVE)).thenReturn(5l);
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ERROR)).thenReturn(0l);

    disableDefinitionExecutor.handle(definitionDetails);
    mocked.verify(Mockito.times(1), () -> WorkflowLogger.logError(Mockito.anyString()));
  }

  @Test
  public void cleanupDefinitionProcessor_checkEligibility_whenCleanActiveProcessIsEnabled() {
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);

    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ACTIVE)).thenReturn(5l);
    Mockito.when(
        processDetailsRepository.countByDefinitionDetailsAndProcessStatus(definitionDetails,
            ProcessStatus.ERROR)).thenReturn(0l);

    disableDefinitionExecutor.handle(definitionDetails);

    Mockito.verify(processDetailsRepository, Mockito.times(0))
        .countByDefinitionDetailsAndProcessStatus(definitionDetails, ProcessStatus.ERROR);
    Mockito.verify(processDetailsRepository, Mockito.times(1))
        .countByDefinitionDetailsAndProcessStatus(definitionDetails, ProcessStatus.ACTIVE);
  }

  @Test
  public void testGetName() {
    Assert.assertEquals(BatchCleanupStatusType.DISABLE_DEFINITION,
        disableDefinitionExecutor.getName());
  }



  @Test
  public void cleanupDefinitionProcessorDisable_TestUpdateScheduleExecuted() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);
    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);
    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenReturn(Mockito.mock(AppConnectSaveWorkflowResponse.class));
    disableDefinitionExecutor.handle(definitionDetails);
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(0)).execute(Mockito.any());
  }

  @Test
  public void cleanupDefinitionProcessorDisable_TestUpdateScheduleExecutedScheduling() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);
    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);
    Mockito.when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
            .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
            .thenReturn(Mockito.mock(AppConnectSaveWorkflowResponse.class));
    disableDefinitionExecutor.handle(definitionDetails);
    Mockito.verify(updateEventSchedulingTask, Mockito.times(0)).execute(Mockito.any());
  }

  @Test
  public void cleanupDefinitionProcessorDisable_TestUpdateScheduleNotExecuted() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);
    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);
    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
        .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
        .thenReturn(Mockito.mock(AppConnectSaveWorkflowResponse.class));
    Mockito.when(eventScheduleHelper.prepareScheduleStatusUpdateTask(Mockito.any(), Mockito.any()))
        .thenReturn(updateStatusEventSchedulerTask);
    disableDefinitionExecutor.handle(definitionDetails);
    Mockito.verify(updateStatusEventSchedulerTask, Mockito.times(1)).execute(Mockito.any());
  }

  @Test
  public void cleanupDefinitionProcessorDisable_TestUpdateScheduleNotExecutedScheduling() {
    final Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
    definitionDetails.setModifiedDate(timestamp);
    authDetails.setOwnerId(123L);
    authDetailsDto.setOwnerId(123L);
    Mockito.when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(true);
    Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(true);
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(any())).thenReturn(authDetails);
    Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
            .thenReturn(authDetailsDto);
    Mockito.when(appConnectService.activateDeactivateActionWorkflow(any(), any(), anyBoolean()))
            .thenReturn(Mockito.mock(AppConnectSaveWorkflowResponse.class));
    Mockito.when(eventScheduleHelper.prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class)))
            .thenReturn(updateEventSchedulingTask);
    disableDefinitionExecutor.handle(definitionDetails);
    Mockito.verify(updateEventSchedulingTask, Mockito.times(1)).execute(Mockito.any());
  }

}
