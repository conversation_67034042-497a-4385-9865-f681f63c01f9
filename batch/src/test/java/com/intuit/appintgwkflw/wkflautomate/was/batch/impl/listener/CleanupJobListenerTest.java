package com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener;

import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CleanupJobListenerTest {

  @InjectMocks
  private CleanupJobListener cleanupJobListener;

  @Mock
  private WorkflowLogger workflowLogger;


  @Test
  public void testBeforeWrite() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("123");
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetails);
    cleanupJobListener.beforeWrite(list);

  }

  @Test
  public void testAfterWrite() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("123");
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetails);
    cleanupJobListener.afterWrite(list);
  }

  @Test
  public void testOnWriteError() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("123");
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetails);
    Exception exception = new Exception();
    cleanupJobListener.onWriteError(exception, list);
  }

}