package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.HistoryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ProcessCleanupHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CLEANUP_PROCESS_STEP_NAME;
import static org.mockito.ArgumentMatchers.any;

public class CleanupJobProcessImplTest {

    private CleanupJobProcessImpl cleanupJobProcessImpl;
    @Mock
    private BatchJobConfigDetails batchJobConfigDetails;
    @Mock
    private BatchJobConfig batchJobConfig;

    @Mock
    private HistoryConfig historyConfig;
    @Mock
    private EntityManagerFactory entityManagerFactory;
    @Mock
    private BatchJobInitContext batchJobInitContext;
    @Mock
    private StepBuilderFactory stepBuilderFactory;

    @Mock
    private ProcessCleanupHelper processCleanupHelper;

    @Mock
    @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
    private PlatformTransactionManager transactionManager;

    private MockedStatic<WorkflowLogger> mocked;
    private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
    @Mock
    private ItemReader<ProcessDetails> reader;
    @Mock
    private ItemProcessor<ProcessDetails, ProcessDetails> processor;
    @Mock
    private ItemWriter<ProcessDetails> writer;
    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        jobConfig = new HashMap<>();
        jobConfig.put(BatchJobType.CLEANUP_PROCESS, batchJobConfigDetails);
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
        mocked = Mockito.mockStatic(WorkflowLogger.class);
        cleanupJobProcessImpl = new CleanupJobProcessImpl(
                batchJobConfig,
                entityManagerFactory,
                stepBuilderFactory,
                transactionManager, threadPoolTaskExecutor, processCleanupHelper, applicationContext, historyConfig);
    }

    @After
    public void deregister() {
        mocked.reset();
        mocked.close();
    }

    @Test
    public void cleanupProcessReaderForInitial() {
        Mockito.when(batchJobConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        ItemReader<ProcessDetails> itemStreamReader = cleanupJobProcessImpl.reader();
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
    }

    @Test
    public void cleanupProcessWriter() {
        ItemWriter<ProcessDetails> itemWriter = cleanupJobProcessImpl.writer();
        Assert.assertNotNull(itemWriter);
    }

    @Test
    public void cleanupProcessStep() {
        StepBuilder stepBuilder = Mockito.mock(StepBuilder.class);
        AbstractTaskletStepBuilder abstractTaskletStepBuilder = Mockito.mock(
                AbstractTaskletStepBuilder.class);

        SimpleStepBuilder<ProcessDetails, ProcessDetails> simpleStepBuilder = Mockito.mock(
                SimpleStepBuilder.class);
        TaskletStep taskletStep = Mockito.mock(TaskletStep.class);
        Mockito.when(batchJobConfig.getChunkSize()).thenReturn(5);
        Mockito.when(stepBuilderFactory.get(CLEANUP_PROCESS_STEP_NAME)).thenReturn(stepBuilder);
        Mockito.when(stepBuilder.transactionManager(transactionManager)).thenReturn(stepBuilder);
        Mockito.when(
                        stepBuilder.<ProcessDetails, ProcessDetails>chunk(batchJobConfig.getChunkSize()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.reader(any())).thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.writer(any())).thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.listener((CleanupJobListener) any()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(
                        simpleStepBuilder.processor((ItemProcessor<ProcessDetails, ProcessDetails>) any()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.taskExecutor(any()))
                .thenReturn(abstractTaskletStepBuilder);
        Mockito.when(abstractTaskletStepBuilder.build()).thenReturn(taskletStep);
        Step step = cleanupJobProcessImpl.createStep(reader, processor, writer);

        Assert.assertNotNull(step);
    }

    @Test
    public void testGetName() {
        String res = cleanupJobProcessImpl.getName().getName();
        Assert.assertNotNull(res);
    }

    @Test
    public void testGetPriority() {
        Integer priority = cleanupJobProcessImpl.getPriority();
        Assert.assertNotNull(priority);
    }

}