package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.SINGLE_TO_SINGLE_DEFINITION_STEP_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.v4.workflows.Definition;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.persistence.EntityManagerFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;


public class SingleToSingleDefinitionMigrationImplTest {

    private SingleToSingleDefinitionMigrationImpl singleToSingleDefinitionMigration;

    @Mock
    private BatchJobConfigDetails batchJobConfigDetails;
    @Mock
    private BatchJobConfig batchConfig;
    @Mock
    private EntityManagerFactory entityManagerFactory;
    @Mock
    private BatchJobInitContext batchJobInitContext;
    @Mock
    private StepBuilderFactory stepBuilderFactory;
    @Mock
    private TemplateDetailsRepository templateDetailsRepository;
    @Mock
    private TemplateDetails templateDetails;
    @Mock
    private Optional<TemplateDetails> templateDetailsOptional;
    @Mock
    @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
    private PlatformTransactionManager transactionManager;
    private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
    @Mock
    private MigrationServiceHelper migrationServiceHelper;
    @Mock
    private Definition mockDefinition;
    @Mock
    private ItemReader<DefinitionDetails> reader;
    @Mock
    private ItemProcessor<DefinitionDetails, DefinitionDetails> processor;
    @Mock
    private ItemWriter<DefinitionDetails> writer;
    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private MetricLogger metricLogger;

    @Mock
    private DefinitionDetailsRepository definitionDetailsRepository;


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        jobConfig = new HashMap<>();
        jobConfig.put(BatchJobType.SINGLE_TO_SINGLE_DEFINITION_MIGRATION, batchJobConfigDetails);
        Mockito.when(batchConfig.getStepConfig()).thenReturn(jobConfig);
        singleToSingleDefinitionMigration =
                new SingleToSingleDefinitionMigrationImpl(
                        entityManagerFactory,
                        templateDetailsRepository,
                        batchConfig,
                        threadPoolTaskExecutor,
                        stepBuilderFactory,transactionManager,
                        applicationContext,
                        metricLogger
                );
    }


    @Test
    public void migrateDefinitionReaderForInitial() {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getVersion()).thenReturn(15);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'14'");

        ItemReader<DefinitionDetails> itemStreamReader = singleToSingleDefinitionMigration.reader();
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
    }

    @Test
    public void migrateDefinitionReaderForAllVersion() {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getVersion()).thenReturn(15);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("");

        ItemReader<DefinitionDetails> itemStreamReader = singleToSingleDefinitionMigration.reader();
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
    }

    @Test
    public void migrateDefinitionReaderError() {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getVersion()).thenReturn(15);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'16'");

        ItemReader<DefinitionDetails> itemStreamReader = singleToSingleDefinitionMigration.reader();
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
    }

    @Test(expected = IndexOutOfBoundsException.class)
    public void migrateDefinitionReaderException() {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(batchJobConfigDetails.getTemplateVersion())
                .thenThrow(IndexOutOfBoundsException.class);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getVersion()).thenReturn(15);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(batchJobConfigDetails.getTemplateVersion()).thenReturn("'16'");

        ItemReader<DefinitionDetails> itemStreamReader = singleToSingleDefinitionMigration.reader();
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
        Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
    }

    @Test
    public void migrateDefinitionWriter() {
        ItemWriter<DefinitionDetails> itemWriter = singleToSingleDefinitionMigration.writer();
        Assert.assertNotNull(itemWriter);
    }

    @Test
    public void migrateDefinitionProcessor() throws Exception {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(templateDetails.getId()).thenReturn("2");
        Mockito.when(templateDetails.getVersion()).thenReturn(20);
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("111");
        definitionDetails.setTemplateDetails(TemplateDetails.builder().templateName("xyz").version(19).build());
        Mockito.when(templateDetailsRepository.findByTemplateId(Mockito.any()))
                .thenReturn(templateDetails);
        Mockito.when(definitionDetailsRepository.findByDefinitionId(Mockito.any())).thenReturn(Optional.of(definitionDetails));

        ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = singleToSingleDefinitionMigration.processor();
        DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
        Assert.assertNotNull(definitionDetails1);

    }

    @Test
    public void migrateDefinitionProcessorException() throws Exception {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(templateDetails.getId()).thenReturn("2");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("111");

        Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
                .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_OFFLINE_TICKET));

        Mockito.when(
                        migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockDefinition);

        ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = singleToSingleDefinitionMigration.processor();
        DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
        Assert.assertNull(definitionDetails1);
    }

    @Test
    public void migrateDefinitionProcessorElseException() throws Exception {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(templateDetails.getId()).thenReturn("2");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("111");

        Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
                .thenThrow(
                        new WorkflowGeneralException(WorkflowError.DEFINITION_MIGRATION_FAILED));

        Mockito.when(
                        migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockDefinition);

        ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = singleToSingleDefinitionMigration.processor();
        DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
        Assert.assertNull(definitionDetails1);
    }

    @Test
    public void migrateDefinitionProcessorException2() throws Exception {
        Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
        Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn("customReminder");
        Mockito.when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(templateDetailsOptional);
        Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
        Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
        Mockito.when(templateDetails.getId()).thenReturn("2");
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("111");

        Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
                .thenThrow(IndexOutOfBoundsException.class);

        Mockito.when(
                        migrationServiceHelper.migrateDefinition(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockDefinition);

        ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor = singleToSingleDefinitionMigration.processor();
        DefinitionDetails definitionDetails1 = itemProcessor.process(definitionDetails);
        Assert.assertNull(definitionDetails1);
    }


    @Test
    public void migrateWorkflowDefinitionStep() {
        StepBuilder stepBuilder = Mockito.mock(StepBuilder.class);
        AbstractTaskletStepBuilder abstractTaskletStepBuilder = Mockito.mock(
                AbstractTaskletStepBuilder.class);

        SimpleStepBuilder<DefinitionDetails, DefinitionDetails> simpleStepBuilder = Mockito.mock(
                SimpleStepBuilder.class);
        TaskletStep taskletStep = Mockito.mock(TaskletStep.class);
        Mockito.when(batchConfig.getChunkSize()).thenReturn(5);
        Mockito.when(stepBuilderFactory.get(SINGLE_TO_SINGLE_DEFINITION_STEP_NAME))
                .thenReturn(stepBuilder);
        Mockito.when(stepBuilder.transactionManager(transactionManager)).thenReturn(stepBuilder);
        Mockito.when(
                        stepBuilder.<DefinitionDetails, DefinitionDetails>chunk(batchConfig.getChunkSize()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.reader(Mockito.any())).thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.writer(Mockito.any())).thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.listener((CleanupJobListener) Mockito.any()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.processor(
                        (ItemProcessor<DefinitionDetails, DefinitionDetails>) Mockito.any()))
                .thenReturn(simpleStepBuilder);
        Mockito.when(simpleStepBuilder.taskExecutor(Mockito.any()))
                .thenReturn(abstractTaskletStepBuilder);
        Mockito.when(abstractTaskletStepBuilder.build()).thenReturn(taskletStep);
        Step step = singleToSingleDefinitionMigration.createStep(reader, processor, writer);

        Assert.assertNotNull(step);
    }

    @Test
    public void testGetName() {
        String res = singleToSingleDefinitionMigration.getName().getName();
        Assert.assertNotNull(res);
    }

    @Test
    public void testGetPriority() {
        Integer priority = singleToSingleDefinitionMigration.getPriority();
        Assert.assertNotNull(priority);
    }

}
