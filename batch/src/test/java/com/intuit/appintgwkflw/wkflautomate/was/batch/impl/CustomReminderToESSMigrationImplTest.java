package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_REMINDER_TO_ESS_STEP_NAME;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.impl.listener.CleanupJobListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveEventScheduleWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveScheduleDetailsInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.step.builder.AbstractTaskletStepBuilder;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.TaskletStep;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;

public class CustomReminderToESSMigrationImplTest {
  private CustomReminderToESSMigrationImpl customReminderToESSMigration;
  @Mock private BatchJobConfigDetails batchJobConfigDetails;
  @Mock private BatchJobConfig batchConfig;
  @Mock private EventScheduleHelper eventScheduleHelper;
  @Mock private StepBuilderFactory stepBuilderFactory;
  @Mock private TemplateDetails templateDetails;
  @Mock private Optional<TemplateDetails> templateDetailsOptional;
  @Mock private EntityManagerFactory entityManagerFactory;
  @Mock private ThreadPoolTaskExecutor threadPoolTaskExecutor;
  @Mock private MetricLogger metricLogger;

  @Mock
  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private PlatformTransactionManager transactionManager;

  private Map<BatchJobType, BatchJobConfigDetails> jobConfig;
  @Mock private ItemReader<DefinitionDetails> reader;
  @Mock private ItemProcessor<DefinitionDetails, DefinitionDetails> processor;
  @Mock private ItemWriter<DefinitionDetails> writer;
    @Mock
    private ApplicationContext applicationContext;
  private final String workflowName = "customReminder";

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    jobConfig = new HashMap<>();
    jobConfig.put(BatchJobType.CUSTOM_REMINDER_TO_ESS_MIGRATION, batchJobConfigDetails);
    Mockito.when(batchConfig.getStepConfig()).thenReturn(jobConfig);
    Mockito.when(batchJobConfigDetails.getTemplateIds()).thenReturn("'template-id1','template-id2'");
    Mockito.when(batchJobConfigDetails.getRecordTypes()).thenReturn("'invoice', 'bill'");
    customReminderToESSMigration = new CustomReminderToESSMigrationImpl(
            entityManagerFactory,
            batchConfig,
            threadPoolTaskExecutor,
            stepBuilderFactory,
            transactionManager, eventScheduleHelper, applicationContext, metricLogger);
  }

  @Test
  public void migrateDefinitionReaderForForAllOwnerIds() {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn(workflowName);
    ItemReader<DefinitionDetails> itemStreamReader = customReminderToESSMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), 10);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionReaderForInitialException() {
    // it will throw error // illegal argument exception
    Mockito.when(batchConfig.getBatchSize()).thenReturn(-1);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn(workflowName);
    ItemReader<DefinitionDetails> itemStreamReader = customReminderToESSMigration.reader();
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPageSize(), -1);
    Assert.assertEquals(((JpaPagingItemReader) itemStreamReader).getPage(), 0);
  }

  @Test
  public void migrateDefinitionWriter() {
    ItemWriter<DefinitionDetails> itemWriter = customReminderToESSMigration.writer();
    Assert.assertNotNull(itemWriter);
  }

  @Test
  public void migrateDefinitionProcessor() throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn(workflowName);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    Mockito.when(templateDetails.getVersion()).thenReturn(20);
    Mockito.when(templateDetails.getTemplateName()).thenReturn(workflowName);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setOwnerId(1234567890L);
    definitionDetails.setTemplateDetails(templateDetails);
    Mockito.doNothing().when(eventScheduleHelper).migrateWorkflowScheduleActionsToESS(Mockito.any());
    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor =
        customReminderToESSMigration.processor();
    itemProcessor.process(definitionDetails);
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).migrateWorkflowScheduleActionsToESS(Mockito.any());
  }

  @Test
  public void migrateDefinitionProcessor_ThrowException()
      throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn(workflowName);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    Mockito.when(templateDetails.getVersion()).thenReturn(20);
    Mockito.when(templateDetails.getTemplateName()).thenReturn(workflowName);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setOwnerId(1234567890L);
    definitionDetails.setTemplateDetails(templateDetails);
    Mockito.doThrow(new RuntimeException("Test")).when(eventScheduleHelper).migrateWorkflowScheduleActionsToESS(Mockito.any());
    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor =
        customReminderToESSMigration.processor();
    itemProcessor.process(definitionDetails);
    Mockito.verify(eventScheduleHelper, Mockito.times(1)).migrateWorkflowScheduleActionsToESS(Mockito.any());
    Assert.assertNotNull(definitionDetails.getModifiedDate());
  }


  @Test
  public void migrateDefinitionProcessor_ThrowWGException()
      throws Exception {
    Mockito.when(batchConfig.getBatchSize()).thenReturn(10);
    Mockito.when(batchJobConfigDetails.getTemplateName()).thenReturn(workflowName);
    Mockito.when(templateDetailsOptional.get()).thenReturn(templateDetails);
    Mockito.when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Mockito.when(templateDetails.getId()).thenReturn("2");
    Mockito.when(templateDetails.getVersion()).thenReturn(20);
    Mockito.when(templateDetails.getTemplateName()).thenReturn(workflowName);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setOwnerId(1234567890L);
    definitionDetails.setTemplateDetails(templateDetails);
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.ESS_MIGRATION_CREATE_SCHEDULE_TASKS))
        .when(eventScheduleHelper).migrateWorkflowScheduleActionsToESS(Mockito.any());
    ItemProcessor<DefinitionDetails, DefinitionDetails> itemProcessor =
        customReminderToESSMigration.processor();
    itemProcessor.process(definitionDetails);
    Mockito.verify(eventScheduleHelper, Mockito.times(1))
        .migrateWorkflowScheduleActionsToESS(Mockito.any());
    Assert.assertNotNull(definitionDetails.getModifiedDate());
  }
  @Test
  public void cleanupWorkflowDefinitionStep() {
    StepBuilder stepBuilder = Mockito.mock(StepBuilder.class);
    AbstractTaskletStepBuilder abstractTaskletStepBuilder =
        Mockito.mock(AbstractTaskletStepBuilder.class);

    SimpleStepBuilder<DefinitionDetails, DefinitionDetails> simpleStepBuilder =
        Mockito.mock(SimpleStepBuilder.class);
    TaskletStep taskletStep = Mockito.mock(TaskletStep.class);
    Mockito.when(batchConfig.getChunkSize()).thenReturn(5);
    Mockito.when(stepBuilderFactory.get(CUSTOM_REMINDER_TO_ESS_STEP_NAME)).thenReturn(stepBuilder);
    Mockito.when(stepBuilder.transactionManager(transactionManager)).thenReturn(stepBuilder);
    Mockito.when(
        stepBuilder.<DefinitionDetails, DefinitionDetails>chunk(batchConfig.getChunkSize()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.reader(any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.writer(any())).thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.listener((CleanupJobListener) any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(
        simpleStepBuilder.processor(
            (ItemProcessor<DefinitionDetails, DefinitionDetails>) any()))
        .thenReturn(simpleStepBuilder);
    Mockito.when(simpleStepBuilder.taskExecutor(any())).thenReturn(abstractTaskletStepBuilder);
    Mockito.when(abstractTaskletStepBuilder.build()).thenReturn(taskletStep);
    Step step = customReminderToESSMigration.createStep(reader, processor, writer);
    Assert.assertNotNull(step);
  }

  @Test
  public void testGetName() {
    String res = customReminderToESSMigration.getName().getName();
    Assert.assertNotNull(res);
  }

  @Test
  public void testGetPriority() {
    Integer priority = customReminderToESSMigration.getPriority();
    Assert.assertNotNull(priority);
  }
}
