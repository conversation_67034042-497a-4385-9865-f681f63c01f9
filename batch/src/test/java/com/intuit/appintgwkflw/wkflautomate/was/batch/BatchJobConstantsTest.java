package com.intuit.appintgwkflw.wkflautomate.was.batch;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BatchJobConstantsTest {

    @Test
    public void testFinalClass() {

        BatchJobConstants batchJobConstants = new BatchJobConstants();
        Assert.assertNotNull(batchJobConstants);
    }
}
