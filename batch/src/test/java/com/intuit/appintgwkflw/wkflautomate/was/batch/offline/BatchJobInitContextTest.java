package com.intuit.appintgwkflw.wkflautomate.was.batch.offline;

import static org.mockito.Mockito.times;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class BatchJobInitContextTest {

  // This is ID 2.0 auth header
  private final String authHeaderWithAuthId = "Intuit_IAM_Authentication intuit_appid=xyz,intuit_app_secret=ppd,intuit_token=ey=jwt-token*,intuit_userid=9130360743195416,intuit_token_type=IAM-Ticket,intuit_realmid=9130360743195436";
  private final String offlineTicket = "V1-226-X3v2bc99a7p0wmqhfc5hhy";
  @Mock
  private AuthDetailsService authDetailsService;
  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private OfflineTicketClient offlineTicketClient;
  @InjectMocks
  private BatchJobInitContext batchJobInitContext;
  private DefinitionDetails definitionDetails;
  private AuthDetails authDetails;

  @Before
  public void init() {
    authDetails = new AuthDetails();
    authDetails.setSubscriptionId("11");
    definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("111");
    definitionDetails.setOwnerId(1111l);
    definitionDetails.setWorkflowId("11");
    definitionDetails.setModifiedByUserId(11l);
    authDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now().minusMinutes(10)));
  }

  @Test
  public void refreshTicketToContext() {
    Mockito.when(authDetailsService.getAuthDetailsFromRealmId(Mockito.anyString()))
        .thenReturn(authDetails);

    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any()))
            .thenReturn(authHeaderWithAuthId);
    AuthDetailsDto newAuthDetails = batchJobInitContext.refreshTicketToContext(definitionDetails);

    Assert.assertEquals("11", newAuthDetails.getSubscriptionId());
    Mockito.verify(authDetailsService, times(1))
        .getAuthDetailsFromRealmId(Long.toString(definitionDetails.getOwnerId()));
    Mockito.verify(offlineTicketClient, Mockito.times(1)).getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any());

  }

  @Test(expected = WorkflowGeneralException.class)
  public void refreshTicketToContextWithNullDefinition() {
    batchJobInitContext.refreshTicketToContext(null);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void refreshTicketToContextWithNullDefinitionOwnerId() {
    definitionDetails.setOwnerId(null);
    batchJobInitContext.refreshTicketToContext(null);
  }

  @Test
  public void testGenerateAuthDetailsSuccess_WithAuthId(){
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.any()))
            .thenReturn(authHeaderWithAuthId);
    Authorization authorization = batchJobInitContext.generateAuthorization(definitionDetails);
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(Mockito.eq(WASContextEnums.INTUIT_TID), Mockito.any());
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(WASContextEnums.INTUIT_REALMID, authorization.getRealm());
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(WASContextEnums.OWNER_ID, authorization.getRealm());
    Assert.assertTrue(WASContext.isMigrationContext());
    Assert.assertEquals(authorization, WASContext.getAuthContext());
    WASContext.clear();
  }

}