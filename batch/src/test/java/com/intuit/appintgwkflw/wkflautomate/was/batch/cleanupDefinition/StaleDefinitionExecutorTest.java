package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StaleDefinitionHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

public class StaleDefinitionExecutorTest {

    @InjectMocks
    private StaleDefinitionExecutor staleDefinitionExecutor;
    @Mock
    private BatchJobConfig batchJobConfig;
    @Mock
    private BatchJobInitContext batchJobInitContext;
    @Mock
    private StaleDefinitionHelper staleDefinitionHelper;

    @Mock
    private BatchJobConfigDetails batchJobConfigDetails;

    private Map<BatchJobType, BatchJobConfigDetails> jobConfig;

    private MockedStatic<WorkflowLogger> mocked;

    private DefinitionDetails definitionDetails;


    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        jobConfig = new HashMap<>();
        jobConfig.put(BatchJobType.CLEANUP_DEFINITION, batchJobConfigDetails);
        definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("111");
        definitionDetails.setInternalStatus(InternalStatus.STALE_DEFINITION);
        definitionDetails.setOwnerId(123L);
        definitionDetails.setWorkflowId("12345");
        Mockito.when(batchJobConfig.getStepConfig()).thenReturn(jobConfig);
        mocked = Mockito.mockStatic(WorkflowLogger.class);
    }

    @After
    public void deregister() {
        mocked.reset();
        mocked.close();
    }

    @Test
    public void cleanupStaleDefinitionProcessor() {
        Mockito.when(batchJobConfig.getBatchSize()).thenReturn(500);

        TemplateDetails templateDetails = new TemplateDetails();
        definitionDetails.setTemplateDetails(templateDetails);
        definitionDetails.getTemplateDetails().setDefinitionType(DefinitionType.SINGLE);

        AuthDetailsDto authDetails = new AuthDetailsDto();
        authDetails.setAuthDetailsId("11");
        authDetails.setSubscriptionId("123");

        Mockito.when(batchJobConfigDetails.isCleanActiveProcess()).thenReturn(false);
        Mockito.when(batchJobConfigDetails.isCleanErrorProcess()).thenReturn(false);
        Mockito.when(batchJobInitContext.refreshTicketToContext(definitionDetails))
                .thenReturn(authDetails);
        staleDefinitionExecutor.handle(definitionDetails);

        Mockito.verify(staleDefinitionHelper, Mockito.times(1)).deleteStaleDefinitionAndEndedProcesses(any());
    }

    @Test
    public void testGetName() {
        Assert.assertEquals(BatchCleanupStatusType.STALE_DEFINITION,
                staleDefinitionExecutor.getName());
    }

}