package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CleanupDefinitionHandlersTest {

  @Mock
  private DisableDefinitionExecutor disableDefinitionExecutor;

  @SuppressWarnings("deprecation")
  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(disableDefinitionExecutor.getName())
        .thenReturn(BatchCleanupStatusType.DISABLE_DEFINITION);
    CleanupDefinitionHandlers.addHandler(disableDefinitionExecutor.getName(),
        disableDefinitionExecutor);
  }

  @Test
  public void getTestNUll() {
    CleanupDefinitionExecutor handler = CleanupDefinitionHandlers.getHandler(null);
    Assert.assertNull(handler);
  }

  @Test
  public void getTestAction() {
    CleanupDefinitionExecutor handler = CleanupDefinitionHandlers.getHandler(
        BatchCleanupStatusType.DISABLE_DEFINITION);
    Assert.assertNotNull(handler);
  }

  @Test
  public void containsFalse() {
    Assert.assertFalse(CleanupDefinitionHandlers.contains(null));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(
        CleanupDefinitionHandlers.contains(BatchCleanupStatusType.DISABLE_DEFINITION));
  }
}