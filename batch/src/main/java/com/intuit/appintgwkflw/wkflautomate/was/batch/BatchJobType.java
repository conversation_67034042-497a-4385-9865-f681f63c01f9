package com.intuit.appintgwkflw.wkflautomate.was.batch;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Enum to store different steps of batch job
 * This enum maps to the stepConfig inside Batch job configuration
 */
@AllArgsConstructor
@Getter
public enum BatchJobType {
  CLEANUP_DEFINITION("cleanupDefinition"),
  USER_TO_SINGLE_DEFINITION_MIGRATION("userToSingleDefinitionMigration"),
  SINGLE_TO_SINGLE_DEFINITION_MIGRATION("singleToSingleDefinitionMigration"),
  CUSTOM_REMINDER_TO_ESS_MIGRATION("customReminderToESSMigration"),
  SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION("scheduledActionsESSToSchedulingSvcMigration"),
  SCHEDULING_SVC_MIGRATION_CLEANUP("schedulingSvcMigrationCleanup"),
  CACHE_WARMUP("cacheWarmup"),
  CLEANUP_PROCESS("cleanupProcess");
  private final String name;
}
