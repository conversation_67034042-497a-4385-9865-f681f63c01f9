package com.intuit.appintgwkflw.wkflautomate.was.batch.offline;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.v4.Authorization;

import java.util.UUID;

import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Helper class which is responsible to update the offline refresh ticket to
 * contextHandler
 */
@Component
@AllArgsConstructor
public class BatchJobInitContext {

  private final AuthDetailsService authDetailsService;
  private final WASContextHandler contextHandler;

  private final OfflineTicketClient offlineTicketClient;

  /**
   * @param definitionDetails
   * @return Returns the AuthDetails with the updated offline ticket. The was context also gets
   * initialised context.
   */
  public AuthDetailsDto refreshTicketToContext(DefinitionDetails definitionDetails) {
    try {
      boolean isInvalidDefinition =
          definitionDetails == null || definitionDetails.getOwnerId() == null;
      WorkflowVerfiy.verify(isInvalidDefinition, WorkflowError.INVALID_INPUT);

      AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(
          Long.toString(definitionDetails.getOwnerId()));

      AuthDetailsDto authDetailsDto = new AuthDetailsDto();
      BeanUtils.copyProperties(authDetails, authDetailsDto);

      generateAuthorization(definitionDetails);
      return authDetailsDto;
    } catch (Exception e) {
      throw new WorkflowGeneralException(e);
    }
  }


  /**
   * @param definitionDetails Inits the context with tid and offline ticket
   */
  public Authorization generateAuthorization(DefinitionDetails definitionDetails){
    String offlineTicketHeader = offlineTicketClient
            .getSystemOfflineHeaderWithContextRealmForOfflineJob(Long.toString(definitionDetails.getOwnerId()));
    Authorization authorization = new Authorization(offlineTicketHeader);
    contextHandler.addKey(WASContextEnums.INTUIT_TID, BatchJobConstants.BATCH_PREFIX_TID + UUID.randomUUID());

    contextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
    contextHandler.addKey(WASContextEnums.INTUIT_REALMID, authorization.getRealm());
    contextHandler.addKey(WASContextEnums.OWNER_ID, authorization.getRealm());
    WASContext.setMigrationContext(true);
    WASContext.setAuthContext(authorization);
    return authorization;
  }
}
