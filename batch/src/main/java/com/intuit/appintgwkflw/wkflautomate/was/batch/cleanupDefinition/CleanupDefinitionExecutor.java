package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logError;
import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logInfo;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Implementation of abstract class to be leveraged for any definition cleanup batch operation. Each
 * of the operation related to the definition cleanup need to extend this class and implement the
 * handle function.
 *
 * <AUTHOR>
 */
public abstract class CleanupDefinitionExecutor {

  /**
   * Function for the implementation of a cleanup operation.
   *
   * @param definitionDetails The details of the definition concerned
   */
  public abstract void handle(DefinitionDetails definitionDetails);

  /**
   * @return The BatchCleanupStatusType for the cleanup executor inheritors.
   */
  public abstract BatchCleanupStatusType getName();

  /**
   * Handle exceptions thrown dusing the cleanup batch operations.
   *
   * @param ex                  Exceptions thrown
   * @param definitionDetails   The details of the definition concerned
   * @param batchJobInitContext Responsible to update the offline refresh ticket to contextHandler
   * @param workflowError       Error type to be thrown based on the cleanUp operation type
   * @param processName         Type of the cleanUp process
   */
  protected void handleBatchExceptions(Exception ex, DefinitionDetails definitionDetails,
      BatchJobInitContext batchJobInitContext, WorkflowError workflowError, String processName) {

    if (ex instanceof WorkflowGeneralException) {
      WorkflowGeneralException exception = (WorkflowGeneralException) ex;
      // Auth ticket expires in 2-10 minutes, so update auth ticket - row will be picked next day
      if (ObjectUtils.isNotEmpty(exception.getError())
          && exception.getError().getMessage().equals(workflowError.name())) {
        batchJobInitContext.refreshTicketToContext(definitionDetails);
        logInfo(String.format("step=%sInitRefresh", processName));
      }
    }
    // Won't fetch in the current batch
    definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
    logError(String.format("step=%sInitFailure, status=failed, error=", processName) + ex);
  }

  /**
   * Check for the eligibility of the definition to be cleaned up on the basis of the presence of
   * active or errored processes.
   *
   * @param processDetailsRepository Stores the details for all the processes
   * @param definitionDetails        The details of the definition concerned
   * @param batchJobConfigDetails    Specify the config related to the batch job helpful for
   *                                 handling whether to take in consideration the errored/active
   *                                 processes
   * @param workflowError            Error type to be thrown based on the cleanUp operation type
   */
  protected void isEligible(
      ProcessDetailsRepository processDetailsRepository,
      DefinitionDetails definitionDetails,
      BatchJobConfigDetails batchJobConfigDetails,
      WorkflowError workflowError) {
    long errorProcessCount = 0;
    long activeProcessCount = 0;

    if (!batchJobConfigDetails.isCleanActiveProcess()) {
      activeProcessCount =
          processDetailsRepository.countByDefinitionDetailsAndProcessStatus(
              definitionDetails, ProcessStatus.ACTIVE);
    }

    if (!batchJobConfigDetails.isCleanErrorProcess()) {
      errorProcessCount =
          processDetailsRepository.countByDefinitionDetailsAndProcessStatus(
              definitionDetails, ProcessStatus.ERROR);
    }

    WorkflowLogger.logInfo(
        "step=isEligible, definitionId=%s, activeProcessCount=%s, errorProcessCount=%s",
        definitionDetails.getDefinitionId(), activeProcessCount, errorProcessCount);

    WorkflowVerfiy.verify((activeProcessCount > 0 || errorProcessCount > 0), workflowError);
  }

  protected BatchJobConfigDetails getBatchJobConfigDetails(BatchJobConfig batchJobConfig) {
    return batchJobConfig.getStepConfig().get(BatchJobType.CLEANUP_DEFINITION);
  }
}
