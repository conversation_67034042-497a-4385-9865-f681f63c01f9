package com.intuit.appintgwkflw.wkflautomate.was.batch;

import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.job.builder.SimpleJobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.ListIterator;

/**
 * <AUTHOR>
 * Initialises bean of Batch Cleanup Job
 * This Job will execute all the steps defined as implementations of BatchService
 */
@Component
@AllArgsConstructor
@ConditionalOnExpression("${batch-job.enabled:false}")
public class BatchJobInit {

  //List of all BatchServices which are enabled
  private List<BatchService<?>> batchServices;
  private ApplicationContext applicationContext;
  private JobBuilderFactory jobBuilderFactory;
  private BatchJobConfig batchJobConfig;

  /**
   *  Creates a job with all implementations of BatchService.java as independent steps
   *  Each step will be executed sequentially
   */

  @Bean
  public Job workflowBatchJob(){

    batchServices.sort(Comparator.comparingInt(BatchService::getPriority));

    ListIterator<BatchService<?>> iterator = batchServices.listIterator();

    SimpleJobBuilder jobBuilder = jobBuilderFactory
        .get(batchJobConfig.getJobName())
        .incrementer(new RunIdIncrementer())
        .start((Step)applicationContext.getBean(getStepBeanName(iterator.next())));

    while(iterator.hasNext()){
      Step step = (Step) applicationContext.getBean(getStepBeanName(iterator.next()));
      jobBuilder = jobBuilder.next(step);
    }

    return jobBuilder.build();

  }

  private String getStepBeanName(BatchService<?> batchService){
    return batchService.getName().name().concat(BatchJobConstants.STEP);
  }
}
