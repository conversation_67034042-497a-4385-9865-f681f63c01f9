package com.intuit.appintgwkflw.wkflautomate.was.batch;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;

import java.util.Date;
import java.util.UUID;

import lombok.AllArgsConstructor;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Start up script for all batch related activities except OfflineTicketRefresh. User can configure the
 * cron expression in the config. It uses Spring batch to Handle concurrency across pods.
 * All the steps will be executed sequentially to avoid over consumption of resources
 */
@AllArgsConstructor
@Component
@EnableScheduling
@ConditionalOnExpression("${batch-job.enabled:false}")
public class BatchJobScheduler {

  private Job workflowBatchJob;
  private JobLauncher jobLauncher;
  private JobRepository jobRepository;
  private WASContextHandler contextHandler;
  private BatchJobConfig batchJobConfig;
  private MetricLogger metricLogger;

  @Scheduled(cron = "${batch-job.cron}")
  @ConditionalOnExpression("T(com.intuit.appintgwkflw.wkflautomate.was.core.util.DeploymentUtil).isAppDeploymentPod()")
  @Metric(name = MetricName.WAS_BATCH_OPERATION, type = Type.APPLICATION_METRIC)
  public void scheduleBatchJob() {
    contextHandler.addKey(WASContextEnums.INTUIT_TID, UUID.randomUUID().toString());
    final Date currentDate = DateUtils.getDateWithoutTimestamp(new Date());

    JobParameters params =
        new JobParametersBuilder().addDate(batchJobConfig.getJobName(), currentDate, true)
            .toJobParameters();

    JobExecution priorExecution =
        jobRepository.getLastJobExecution(workflowBatchJob.getName(), params);
    if (priorExecution != null) {
      // Ensure Job runs in one pod only. So checking for Running.
      if (priorExecution.getStatus() == BatchStatus.COMPLETED || priorExecution.isRunning()) {
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message("Batch Job already completed or already running")
                    .className(this.getClass().getName()));
        return;
      }
    }
    try {
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Starting the Batch Job")
                  .className(this.getClass().getName()));
      // run the job
      JobExecution jobExecution = jobLauncher.run(workflowBatchJob, params);

      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(
                      "Batch job done with status=%s; jobExecutionId=%s; jobId=%s;",
                      jobExecution.getStatus().name(),
                      jobExecution.getId(),
                      jobExecution.getJobId())
                  .className(this.getClass().getName()));
      if(jobExecution.getStatus() == BatchStatus.FAILED) {
        metricLogger.logErrorMetric(MetricName.BATCH_JOB, Type.APPLICATION_METRIC, jobExecution.getFailureExceptions().get(0));
      }
    } catch (Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Batch job exited unexpectedly")
                  .className(this.getClass().getName())
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.BATCH_JOB));
      metricLogger.logErrorMetric(MetricName.BATCH_JOB, Type.APPLICATION_METRIC, e);
    }
  }
}