package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import java.util.EnumMap;
import java.util.Map;

/**
 * The class act as the factory for cleanup definition executor handlers.
 *
 * <AUTHOR>
 */
public class CleanupDefinitionHandlers {

  private static final Map<BatchCleanupStatusType, CleanupDefinitionExecutor> TASK_HANDLER_MAP = new EnumMap<>(
      BatchCleanupStatusType.class);

  /**
   * Adds a handler.
   *
   * @param handlerName the handler name
   * @param taskHandler the task handler
   */
  public static void addHandler(BatchCleanupStatusType handlerName,
      CleanupDefinitionExecutor taskHandler) {

    TASK_HANDLER_MAP.put(handlerName, taskHandler);
  }

  /**
   * Gets handler.
   *
   * @param handlerName input handler name
   * @return action handler impl
   */
  public static CleanupDefinitionExecutor getHandler(BatchCleanupStatusType handlerName) {

    return TASK_HANDLER_MAP.get(handlerName);
  }

  /**
   * Contains boolean.
   *
   * @param handlerName input handler name
   * @return true /false if handler is present or not
   */
  public static boolean contains(BatchCleanupStatusType handlerName) {

    return TASK_HANDLER_MAP.containsKey(handlerName);
  }
}
