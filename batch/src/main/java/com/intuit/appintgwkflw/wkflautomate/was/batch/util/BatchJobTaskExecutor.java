package com.intuit.appintgwkflw.wkflautomate.was.batch.util;

import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MdcTaskDecorator;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.BATCH_TASK_EXECUTOR_NAME_PREFIX;

/**
 * <AUTHOR>
 * Common thread pool executor configuration to be used in batchService steps
 */
@Service
@AllArgsConstructor
@ConditionalOnExpression("${batch-job.enabled:false}")
public class BatchJobTaskExecutor {

  private BatchJobConfig batchJobConfig;
  /**
   * Cleanup ThreadPool executor to run all the steps
   *
   * @return {@link ThreadPoolTaskExecutor}
   */
  @Bean
  public ThreadPoolTaskExecutor batchThreadPoolTaskExecutor() {
    ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
    taskExecutor.setTaskDecorator(new MdcTaskDecorator());
    taskExecutor.setCorePoolSize(batchJobConfig.getThreadPoolSize());
    taskExecutor.setThreadNamePrefix(BATCH_TASK_EXECUTOR_NAME_PREFIX);
    taskExecutor.setKeepAliveSeconds(batchJobConfig.getKeepAliveTime());
    return taskExecutor;
  }

  @Bean
  @Scope("prototype")
  public ThreadPoolTaskExecutor customBatchThreadPoolTaskExecutor() {
    ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
    taskExecutor.setTaskDecorator(new MdcTaskDecorator());
    taskExecutor.setCorePoolSize(batchJobConfig.getThreadPoolSize());
    taskExecutor.setThreadNamePrefix(BATCH_TASK_EXECUTOR_NAME_PREFIX);
    taskExecutor.setKeepAliveSeconds(batchJobConfig.getKeepAliveTime());
    return taskExecutor;
  }
}
