package com.intuit.appintgwkflw.wkflautomate.was.batch;

import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import lombok.AllArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.BATCH_TASK_EXECUTOR_NAME_PREFIX;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.CUSTOM_BATCH_THREAD_POOL_TASK_EXECUTOR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.HYPHEN;

/**
 * <AUTHOR>
 *  This interface is used to implement any spring batch service being run in WAS
 *  All the classes that implement BatchService will be run in independent steps as part of a SINGLE job
 *  <B>Every new batch job usecase needs to implement this interface</B>
 */
@AllArgsConstructor
public abstract class BatchService<T> {

  @Autowired
  private BatchJobConfig batchJobConfig;
  @Autowired
  private ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
  @Autowired
  private ApplicationContext applicationContext;

  /**
   *  Provide implementation of spring batch reader
   */
  protected abstract ItemReader<T> reader();

  /**
   * Provide implementation of spring batch processor
   */
  protected abstract ItemProcessor<T,T> processor();

  /**
   * Provide implementation of spring batch writer
   */
  protected abstract ItemWriter<T> writer();


  /**
   * Create a step that will be executed as a part of the batch job independently
   */
  protected abstract Step createStep(ItemReader<T> reader, ItemProcessor<T, T> processor, ItemWriter<T> writer);


  /**
   * Returns the priority of the implementation
   * The order in which steps will be executed, priority=1 will be executed first and so on
   * This field is populated from config
   */
  public Integer getPriority() {
    return getBatchConfig().getPriority();
  }

  /**
   * Returns the name of the BatchServiceImplementation
   * e.g. cleanup_definition, migrate_definition etc
   */
  protected abstract BatchJobType getName();

  protected BatchJobConfigDetails getBatchConfig() {
    return batchJobConfig.getStepConfig().get(getName());
  }

  public int getBatchSize() {
    BatchJobConfigDetails batchJobConfigDetails
            = getBatchConfig();
    return batchJobConfigDetails.getBatchSize()  != 0 ?
            batchJobConfigDetails.getBatchSize(): batchJobConfig.getBatchSize();
  }

  public int getChunkSize() {
    BatchJobConfigDetails batchJobConfigDetails
            = getBatchConfig();
    return batchJobConfigDetails.getChunkSize()  != 0 ?
            batchJobConfigDetails.getChunkSize(): batchJobConfig.getChunkSize();
  }

  public ThreadPoolTaskExecutor getThreadPoolTaskExecutor() {

    BatchJobConfigDetails batchJobConfigDetails
            = getBatchConfig();
    if(batchJobConfigDetails.getThreadPool() == null) {
        return batchThreadPoolTaskExecutor;
    }

    ThreadPoolTaskExecutor customThreadPoolTaskExecutor = (ThreadPoolTaskExecutor) applicationContext.getBean(CUSTOM_BATCH_THREAD_POOL_TASK_EXECUTOR);
    customThreadPoolTaskExecutor.setThreadNamePrefix(BATCH_TASK_EXECUTOR_NAME_PREFIX.concat(getName().name()).concat(HYPHEN));

    customThreadPoolTaskExecutor.setCorePoolSize(batchJobConfigDetails.getThreadPool().getSharedMinThreads() != null ?
                    batchJobConfigDetails.getThreadPool().getSharedMinThreads():
                    batchJobConfig.getThreadPoolSize()
    );

    customThreadPoolTaskExecutor.setMaxPoolSize(batchJobConfigDetails.getThreadPool().getSharedMaxThreads() != null ?
                    batchJobConfigDetails.getThreadPool().getSharedMaxThreads():
                    batchThreadPoolTaskExecutor.getMaxPoolSize()
    );

    return customThreadPoolTaskExecutor;
  }


}
