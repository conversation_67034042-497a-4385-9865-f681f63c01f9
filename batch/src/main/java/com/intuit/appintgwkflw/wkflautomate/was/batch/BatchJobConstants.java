package com.intuit.appintgwkflw.wkflautomate.was.batch;

public final class BatchJobConstants {

  public static final String MASTER_CLEANUP_QUERY = "SELECT * FROM was.de_definition_details where internal_status IN (%s) and model_type = 'bpmn' "
      + "and workflow_id IS NOT NULL and modified_date < '%s' "
      + "and now() < '%s' order by internal_status, owner_id";

  public static final String PROCESS_CLEANUP_QUERY = "SELECT * FROM was.ru_process_details where process_status IN (%s) "
          + "and modified_date < '%s' and now() < '%s'";

  public static final String CLEANUP_QUERY_BY_TEMPLATE =
      "select defn.* from was.de_template_details template JOIN was.de_definition_details defn\n"
          + "    ON template.template_id = defn.template_details_template_id\n"
          + "    where template.template_name IN ('%s') and defn.internal_status IN (%s) and defn.model_type = 'bpmn'"
          + " and defn.workflow_id IS NOT NULL and defn.modified_date < '%s' "
          + "and now() < '%s' order by internal_status, owner_id";

  public static final String BATCH_TASK_EXECUTOR_NAME_PREFIX = "batchExecutor-";
  public static final String CLEANUP_DEFINITION_STEP_NAME = "cleanUpDefinitionStep";
  public static final String BATCH_PREFIX_TID = "batch-job-";
  public static final String USER_TO_SINGLE_DEFINITION_STEP_NAME = "userToSingleDefinitionMigrationStep";

  public static final String SINGLE_TO_SINGLE_DEFINITION_STEP_NAME = "singleToSingleDefinitionMigrationStep";

  public static final String STEP = "_STEP";
  public static final String CLEANUP_DEFINITION = "CLEANUP_DEFINITION";
  public static final String CLEANUP_DEFINITION_STEP = CLEANUP_DEFINITION +"_STEP";
  public static final String CLEANUP_DEFINITION_READER = CLEANUP_DEFINITION +"_READER";
  public static final String CLEANUP_DEFINITION_WRITER = CLEANUP_DEFINITION +"_WRITER";
  public static final String CLEANUP_DEFINITION_PROCESSOR = CLEANUP_DEFINITION +"_PROCESSOR";
  public static final String DISABLE_PROCESS = "disableProcess";
  public static final String DELETE_PROCESS = "cleanUpProcess";
  public static final String DELETE_STALE_DEFINITION = "staleDefinition";

  public static final String CLEANUP_PROCESS = "CLEANUP_PROCESS";

  public static final String CLEANUP_PROCESS_READER = CLEANUP_PROCESS +"_READER";

  public static final String CLEANUP_PROCESS_PROCESSOR = CLEANUP_PROCESS +"_PROCESSOR";

  public static final String CLEANUP_PROCESS_WRITER = CLEANUP_PROCESS +"_WRITER";

  public static final String CLEANUP_PROCESS_STEP = CLEANUP_PROCESS +"_STEP";

  public static final String CLEANUP_PROCESS_STEP_NAME = "cleanUpProcessStep";

  public static final String BATCH_JOB_TASK_EXECUTOR =  "BATCH_JOB_TASK_EXECUTOR";

  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION_JOB_NAME = "USER_TO_SINGLE_DEFINITION_MIGRATION";
  public static final String BATCH_JOB_NAME = "BATCH_JOB";

  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION = "USER_TO_SINGLE_DEFINITION_MIGRATION";
  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION_STEP = USER_TO_SINGLE_DEFINITION_MIGRATION +"_STEP";
  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION_READER = USER_TO_SINGLE_DEFINITION_MIGRATION +"_READER";
  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION_WRITER = USER_TO_SINGLE_DEFINITION_MIGRATION +"_WRITER";
  public static final String USER_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR = USER_TO_SINGLE_DEFINITION_MIGRATION +"_PROCESSOR";

  public static final String SINGLE_TO_SINGLE_DEFINITION_MIGRATION = "SINGLE_TO_SINGLE_DEFINITION_MIGRATION";
  public static final String SINGLE_TO_SINGLE_DEFINITION_MIGRATION_STEP = SINGLE_TO_SINGLE_DEFINITION_MIGRATION +"_STEP";
  public static final String SINGLE_TO_SINGLE_DEFINITION_MIGRATION_READER = SINGLE_TO_SINGLE_DEFINITION_MIGRATION +"_READER";
  public static final String SINGLE_TO_SINGLE_DEFINITION_MIGRATION_WRITER = SINGLE_TO_SINGLE_DEFINITION_MIGRATION +"_WRITER";
  public static final String SINGLE_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR = SINGLE_TO_SINGLE_DEFINITION_MIGRATION +"_PROCESSOR";

  public static final String USER_TO_SINGLE_DEFINITION_TEMPLATE_ID = "SingleTemplateId";

  public static final String MIGRATION_QUERY_ORDER_BY_CLAUSE = "order by owner_id";
  public static final String MIGRATION_QUERY_ORDER_BY_DEFINITION_ID_CLAUSE = "order by definition_id";
  public static final String MIGRATION_QUERY_VERSION_CLAUSE = "and template.version IN (%s) ";

  public static final String BASE_MIGRATION_QUERY = "select defn.* from was.de_template_details template JOIN was.de_definition_details defn " +
      "ON template.template_id = defn.template_details_template_id " +
      "where template.template_name IN ('%s') " +
      "and template.definition_type='%s' " +
      "and defn.record_type IN (%s) " +
      "and (defn.internal_status is null or defn.internal_status='MARKED_FOR_DISABLE') " +
      "and defn.model_type = 'bpmn' " +
      "and defn.workflow_id IS NOT NULL and defn.modified_date < '%s' "+
      "and now() < '%s' ";

  public static final String SINGLE_BASE_MIGRATION_QUERY = "select defn.* from was.de_template_details template JOIN was.de_definition_details defn " +
          "ON template.template_id = defn.template_details_template_id " +
          "where template.template_name IN ('%s') " +
          "and template.definition_type='SINGLE' " +
          "and defn.record_type IN (%s) " +
          "and (defn.internal_status is null or defn.internal_status='MARKED_FOR_DISABLE')  " +
          "and now() < '%s' ";

  public static final String MIGRATION_QUERY_ALL_VERSIONS = BASE_MIGRATION_QUERY + MIGRATION_QUERY_ORDER_BY_CLAUSE;
  public static final String MIGRATION_QUERY_BY_VERSION = BASE_MIGRATION_QUERY + MIGRATION_QUERY_VERSION_CLAUSE + MIGRATION_QUERY_ORDER_BY_CLAUSE;

  public static final String SINGLE_MIGRATION_QUERY_ALL_VERSIONS = SINGLE_BASE_MIGRATION_QUERY + MIGRATION_QUERY_ORDER_BY_CLAUSE;
  public static final String SINGLE_MIGRATION_QUERY_BY_VERSION = SINGLE_BASE_MIGRATION_QUERY + MIGRATION_QUERY_VERSION_CLAUSE + MIGRATION_QUERY_ORDER_BY_CLAUSE;

  public static final String DEFINITION_TYPE_USER = "USER";

  public static final String CUSTOM_REMINDER_TO_ESS_MIGRATION = "CUSTOM_REMINDER_TO_ESS_MIGRATION";
  public static final String CUSTOM_REMINDER_TO_ESS_MIGRATION_STEP =
      CUSTOM_REMINDER_TO_ESS_MIGRATION + "_STEP";
  public static final String CUSTOM_REMINDER_TO_ESS_MIGRATION_READER =
      CUSTOM_REMINDER_TO_ESS_MIGRATION + "_READER";
  public static final String CUSTOM_REMINDER_TO_ESS_MIGRATION_WRITER =
      CUSTOM_REMINDER_TO_ESS_MIGRATION + "_WRITER";
  public static final String CUSTOM_REMINDER_TO_ESS_MIGRATION_PROCESSOR =
      CUSTOM_REMINDER_TO_ESS_MIGRATION + "_PROCESSOR";

  // This query find all the reminder workflow for which ess schedules are not created.
  public static final String BASE_CUSTOM_REMINDER_ESS_MIGRATION_QUERY =
      "select defn.* from  was.de_definition_details defn "
          + "LEFT JOIN was.de_scheduler_details schd ON defn.definition_id = schd.definition_details_definition_id "
          + "where defn.template_details_template_id IN (%s) "
          + "and defn.record_type IN (%s) "
          + "and (defn.internal_status is null or defn.internal_status = 'MARKED_FOR_DISABLED') "
          + "and defn.model_type = 'bpmn' "
          + "and defn.workflow_id IS NOT NULL "
          + "and schd.definition_details_definition_id IS NULL "
          + "and defn.modified_date < '%s' "
          + "and now() < '%s' ";

  public static final String CUSTOM_REMINDER_ESS_MIGRATION_QUERY_ALL_OWNER_IDS =
      BASE_CUSTOM_REMINDER_ESS_MIGRATION_QUERY
          + MIGRATION_QUERY_ORDER_BY_CLAUSE;
  public static final String CUSTOM_REMINDER_TO_ESS_STEP_NAME = "customReminderToESSMigrationStep";

  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION = "SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION";
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_STEP = SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION + "_STEP";
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_READER = SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION + "_READER";
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_WRITER = SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION + "_WRITER";
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_PROCESSOR = SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION + "_PROCESSOR";
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_STEP_NAME = "scheduledActionsESSToSchedulingSvcMigrationStep";

  // This query find all the scheduled action workflows which are created with ESS
  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_QUERY =
           "select defn.* from  was.de_definition_details defn "
                  + "JOIN was.de_scheduler_details schd ON defn.definition_id = schd.definition_details_definition_id "
                  + "where defn.template_details_template_id IN (%s) "
                  + "and (defn.internal_status is null or defn.internal_status = 'MARKED_FOR_DISABLED') "
                  + "and defn.model_type = 'bpmn' "
                  + "and schd.scheduler_action = 'customReminder_customStart' "
                  + "and defn.modified_date < '%s' "
                  + "and schd.is_migrated IS NULL "
                  + "and now() < '%s' ";

  public static final String SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_QUERY_ORDER_BY_DEFINITION_IDS =
          SCHEDULED_ACTIONS_ESS_TO_SCHEDULING_SVC_MIGRATION_QUERY + MIGRATION_QUERY_ORDER_BY_DEFINITION_ID_CLAUSE;
  public static final String SCHEDULING_SVC_MIGRATION_CLEANUP = "SCHEDULING_SVC_MIGRATION_CLEANUP";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_STEP = SCHEDULING_SVC_MIGRATION_CLEANUP + "_STEP";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_READER = SCHEDULING_SVC_MIGRATION_CLEANUP + "_READER";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_WRITER = SCHEDULING_SVC_MIGRATION_CLEANUP + "_WRITER";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_PROCESSOR = SCHEDULING_SVC_MIGRATION_CLEANUP + "_PROCESSOR";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_STEP_NAME = "schedulingSvcMigrationCleanupStep";

    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_QUERY =
            "select defn.* from  was.de_definition_details defn "
                    + "JOIN was.de_scheduler_details schd ON defn.definition_id = schd.definition_details_definition_id "
                    + "where defn.template_details_template_id IN (%s) "
                    + "and (defn.internal_status is null or defn.internal_status = 'MARKED_FOR_DISABLED') "
                    + "and defn.model_type = 'bpmn' "
                    + "and schd.scheduler_action = 'customReminder_customStart' "
                    + "and defn.modified_date < '%s' "
                    + "and schd.is_migrated IS true "
                    + "and now() < '%s' ";
    public static final String SCHEDULING_SVC_MIGRATION_CLEANUP_QUERY_ORDER_BY_DEFINITION_IDS =
            SCHEDULING_SVC_MIGRATION_CLEANUP_QUERY + MIGRATION_QUERY_ORDER_BY_DEFINITION_ID_CLAUSE;

  public static final String CACHE_WARMUP = "CACHE_WARMUP";
  public static final String CACHE_WARMUP_STEP = CACHE_WARMUP +"_STEP";
  public static final String CACHE_WARMUP_READER = CACHE_WARMUP +"_READER";
  public static final String CACHE_WARMUP_WRITER = CACHE_WARMUP +"_WRITER";
  public static final String CACHE_WARMUP_PROCESSOR = CACHE_WARMUP +"_PROCESSOR";
  public static final String CACHE_WARMUP_STEP_NAME = "cacheWarmupStep";

  public static final String CUSTOM_BATCH_THREAD_POOL_TASK_EXECUTOR = "customBatchThreadPoolTaskExecutor";
}
