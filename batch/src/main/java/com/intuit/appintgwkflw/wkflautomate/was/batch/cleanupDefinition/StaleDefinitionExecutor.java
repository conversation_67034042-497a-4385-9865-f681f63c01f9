package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StaleDefinitionHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.DELETE_STALE_DEFINITION;

/**
 * This class is responsible for deleting the stale definition and the corresponding ended processes.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class StaleDefinitionExecutor extends CleanupDefinitionExecutor {

    private BatchJobInitContext batchJobInitContext;

    private StaleDefinitionHelper staleDefinitionHelper;

    @Override
    public void handle(DefinitionDetails definitionDetails) {
        try {
            WorkflowLogger.logInfo(
                    "step=handleBatchStaleDefinition definitionId=%s", definitionDetails.getDefinitionId());

            staleDefinitionHelper.deleteStaleDefinitionAndEndedProcesses(definitionDetails);
        } catch (Exception ex) {
            handleBatchExceptions(ex, definitionDetails,
                    batchJobInitContext, WorkflowError.DELETE_STALE_DEFINITION_FAIL, DELETE_STALE_DEFINITION);
        }
    }

    @Override
    public BatchCleanupStatusType getName() {
        return BatchCleanupStatusType.STALE_DEFINITION;
    }
}