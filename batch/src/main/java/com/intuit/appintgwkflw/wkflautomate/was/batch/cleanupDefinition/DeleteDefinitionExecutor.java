package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.OFFLINE_BATCH_CLEANUP;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.DELETE_PROCESS;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.AppConnectDeleteWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteDeploymentTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.List;

/**
 * Implementation of delete definition batch operation.
 *
 * <AUTHOR> psinha4
 */
@Component
@AllArgsConstructor
public class DeleteDefinitionExecutor extends CleanupDefinitionExecutor {

  private final BatchJobConfig batchJobConfig;
  private final BatchJobInitContext batchJobInitContext;
  private final MetricLogger metricLogger;
  private final ProcessDetailsRepository processDetailsRepository;
  private final AppConnectService appConnectService;
  private final AuthDetailsService authDetailsService;
  private final DefinitionDetailsRepository definitionDetailsRepository;
  private final DataStoreDeleteTaskService dataStoreDeleteTaskService;
  private final BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private final EventScheduleHelper eventScheduleHelper;
  private final SchedulingService schedulingService;
  /**
   * Handler function for deleting a definition if eligible for DB and AppConnect.
   *
   * @param definitionDetails The details of the definition to be deleted
   */
  @Override
  public void handle(DefinitionDetails definitionDetails) {
    try {
      // Check if the definition is marked active or in error state
      WorkflowLogger.logInfo(
          "step=handleBatchDeleteDefinition definitionId=%s", definitionDetails.getDefinitionId());
      isEligible(
          processDetailsRepository, definitionDetails,
          getBatchJobConfigDetails(batchJobConfig),
          WorkflowError.CLEANUP_FAILED_PROCESS_IN_ACTIVE);

      AppConnectDeleteWorkflowTask appConnectDeleteWorkflowTask = new AppConnectDeleteWorkflowTask(
          appConnectService, authDetailsService);
      DataStoreDeleteDefinitionAndProcessTask dataStoreDeleteDefinitionAndProcessTask = new DataStoreDeleteDefinitionAndProcessTask(
          definitionDetailsRepository, definitionDetails.getDefinitionId(),
          dataStoreDeleteTaskService);
      deleteDefinition(definitionDetails, appConnectDeleteWorkflowTask,
          dataStoreDeleteDefinitionAndProcessTask);
    } catch (Exception e) {
      handleBatchExceptions(e, definitionDetails,
          batchJobInitContext, WorkflowError.DELETE_WORKFLOW_FAIL, DELETE_PROCESS);
    }
  }

  @Override
  public BatchCleanupStatusType getName() {
    return BatchCleanupStatusType.DELETE_DEFINITION;
  }

  /**
   * Function to delete a definition from AppConnect and Update DB
   *
   * @param definitionDetails                       The details of the definition to be deleted
   * @param appConnectDeleteWorkflowTask            The predefined task that used for deleting the
   *                                                definition from appConnect
   * @param dataStoreDeleteDefinitionAndProcessTask The predefined task that used for deleting the
   *                                                definition and processes for DB
   */
  private void deleteDefinition(DefinitionDetails definitionDetails,
      AppConnectDeleteWorkflowTask appConnectDeleteWorkflowTask,
      DataStoreDeleteDefinitionAndProcessTask dataStoreDeleteDefinitionAndProcessTask)
      throws WorkflowGeneralException {
    try {
      WorkflowLogger.logInfo("step=deleteDefinitionStarted, definitionId=%s",
          definitionDetails.getDefinitionId());
      AuthDetailsDto authDetails = batchJobInitContext.refreshTicketToContext(definitionDetails
      );
      State inputRequest = deleteInit(definitionDetails, authDetails);
      final RxExecutionChain chain = new RxExecutionChain(inputRequest);

      chain.next(appConnectDeleteWorkflowTask);
      if (definitionDetails.getTemplateDetails().getDefinitionType() != DefinitionType.SINGLE) {
        chain.next(new CamundaDeleteDeploymentTask(
                bpmnEngineDefinitionServiceRest, definitionDetails.getDefinitionId(), true, true));
      }
      chain.next(determineUpdateOrDeleteTask(definitionDetails, inputRequest, definitionDetails.getDefinitionId(), String.valueOf(definitionDetails.getOwnerId())))
              .next(dataStoreDeleteDefinitionAndProcessTask)
              .execute();

      WorkflowLogger.logInfo("step=definitionDeleteComplete, status=SUCCESS definitionId=%s",
          definitionDetails.getDefinitionId());
    } catch (Exception e) {
      metricLogger.logErrorMetric(OFFLINE_BATCH_CLEANUP, Type.APPLICATION_METRIC, e);
      WorkflowLogger.logError(
          "step=definitionDeleteComplete, status=ERROR definitionId=%s error=%s",
          definitionDetails.getDefinitionId(), e);
      throw e;
    }
  }

  /**
   * Initialise the inputRequest for RxExecutionChain creation
   *
   * @param definitionDetails The details of the definition to be deleted
   * @return Initializes the inputState needed for RxChain to execute and also initializes the
   * context.
   */
  private State deleteInit(DefinitionDetails definitionDetails, AuthDetailsDto authDetails) {

    final State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY,
        Long.toString(definitionDetails.getOwnerId()));
    inputRequest.addValue(AsyncTaskConstants.WORKFLOW_ID_KEY, definitionDetails.getWorkflowId());
    inputRequest.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, authDetails.getSubscriptionId());
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, definitionDetails.getDefinitionId());
    inputRequest.addValue(AsyncTaskConstants.DEFINITION_DETAILS, definitionDetails);
    return inputRequest;
  }

  /**
   * Determines the next task to be executed based on whether the actions are custom scheduled and if there are schedule IDs.
   *
   * @param definitionDetails The details of the definition to be deleted.
   * @param inputRequest The current state of the task execution.
   * @param definitionId The ID of the definition.
   * @return The next task to be executed.
   */
  private Task determineUpdateOrDeleteTask(DefinitionDetails definitionDetails, State inputRequest, String definitionId, String realmId) {
    //Todo: Remove this check after complete migration to Scheduling Service
    if (schedulingService.isEnabled(definitionDetails, realmId)) {
      return eventScheduleHelper.prepareSchedulingDeleteTask(inputRequest, definitionDetails.getDefinitionKey());
    }
    return eventScheduleHelper.prepareScheduleStatusUpdateTask(inputRequest, ScheduleStatus.DELETED, definitionId);
  }

}
