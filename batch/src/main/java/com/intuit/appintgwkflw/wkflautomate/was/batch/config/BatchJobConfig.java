package com.intuit.appintgwkflw.wkflautomate.was.batch.config;

import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import lombok.Data;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * Configuration related to batch jobs in WAS
 */
@Configuration
@ConfigurationProperties(prefix = "batch-job")
@EnableBatchProcessing
@Data
public class BatchJobConfig {
  //Defines the schedule of batch job
  private boolean enabled;
  private String cron;
  private String jobName;
  private int threadPoolSize;
  private int batchSize;
  private int chunkSize;
  private int keepAliveTime;
  private Map<BatchJobType, BatchJobConfigDetails> stepConfig;

}
