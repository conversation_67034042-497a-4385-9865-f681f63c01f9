package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Listens to the events for populating the cleanupDefinition executors list.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class BatchDefinitionListener {

  private final List<CleanupDefinitionExecutor> cleanupDefinitionExecutorsList;

  /**
   * Populate factory map for the batch related handlers.
   *
   * @param event
   */
  @EventListener
  public void handleContextRefresh(ContextRefreshedEvent event) {
    cleanupDefinitionExecutorsList.forEach(
        handler -> CleanupDefinitionHandlers.addHandler(handler.getName(), handler)
    );
  }
}
