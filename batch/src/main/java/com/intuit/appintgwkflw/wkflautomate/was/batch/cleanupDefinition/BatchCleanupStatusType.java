package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * This batch cleanup status type is used to fetch the Handler implementation for the cleanup
 * definition batch operation.
 *
 * <AUTHOR>
 */
@Getter
public enum BatchCleanupStatusType {
  DELETE_DEFINITION(InternalStatus.MARKED_FOR_DELETE),
  STALE_DEFINITION(InternalStatus.STALE_DEFINITION),
  DISABLE_DEFINITION(InternalStatus.MARKED_FOR_DISABLE);

  private final static Map<String, BatchCleanupStatusType> reverseBatchCleanupTypeMap = addBatchCleanupTypeMapping();
  private final InternalStatus batchCleanupStatusType;

  BatchCleanupStatusType(InternalStatus batchCleanupStatusType) {
    this.batchCleanupStatusType = batchCleanupStatusType;
  }

  /**
   * @return reverse mapping of handler name to BatchCleanup Handler name.
   */
  private static Map<String, BatchCleanupStatusType> addBatchCleanupTypeMapping() {
    Map<String, BatchCleanupStatusType> batchCleanupTypeHandlerMap = new HashMap<>();
    BatchCleanupStatusType[] batchCleanupStatusTypes = BatchCleanupStatusType.values();
    Arrays.stream(batchCleanupStatusTypes)
        .forEach(
            handler -> {
              batchCleanupTypeHandlerMap.put(handler.getBatchCleanupStatusType().name(), handler);
            });
    return batchCleanupTypeHandlerMap;
  }

  /**
   * Return the type of the batch cleanup status enum of the basis of the handler name.
   *
   * @param batchCleanupType the type of the cleanup operation
   * @return the batch cleanup status type
   */
  public static BatchCleanupStatusType getBatchCleanupStatusTypeFromName(String batchCleanupType) {
    return reverseBatchCleanupTypeMap.get(batchCleanupType);
  }
}
