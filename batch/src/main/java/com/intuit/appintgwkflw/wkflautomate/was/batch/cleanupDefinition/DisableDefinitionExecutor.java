package com.intuit.appintgwkflw.wkflautomate.was.batch.cleanupDefinition;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DisableDeleteWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.OFFLINE_BATCH_DISABLE;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.DISABLE_PROCESS;

/**
 * <AUTHOR> Implementation of delete definition batch operation
 */
@Component
@AllArgsConstructor
public class DisableDefinitionExecutor extends CleanupDefinitionExecutor {

  private BatchJobConfig batchJobConfig;
  private BatchJobInitContext batchJobInitContext;
  private MetricLogger metricLogger;
  private ProcessDetailsRepository processDetailsRepository;
  private AppConnectService appConnectService;
  private AuthDetailsService authDetailsService;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private EventScheduleHelper eventScheduleHelper;
  private SchedulingService schedulingService;

  /**
   * Handler function for disabling a definition if eligible for DB and AppConnect
   *
   * @param definitionDetails The details of the definition to be disabled
   */
  @Override
  public void handle(
      DefinitionDetails definitionDetails) {
    try {
    //Check if the definition is marked active or in error state
      WorkflowLogger.logInfo(
          "step=handleBatchDisableDefinition definitionId=%s", definitionDetails.getDefinitionId());
      isEligible(
          processDetailsRepository, definitionDetails,
          getBatchJobConfigDetails(batchJobConfig),
          WorkflowError.DISABLE_FAILED_PROCESS_IN_ACTIVE);
      DisableDeleteWorkflowTask disableDeleteWorkflowTask = new DisableDeleteWorkflowTask(
          definitionDetailsRepository, appConnectService, authDetailsService, eventScheduleHelper, schedulingService);
      disableDefinition(definitionDetails, disableDeleteWorkflowTask);
    } catch (Exception ex) {
      handleBatchExceptions(ex, definitionDetails,
          batchJobInitContext, WorkflowError.ACTIVATE_DEACTIVATE_WORKFLOW_FAIL, DISABLE_PROCESS);
    }
  }

  @Override
  public BatchCleanupStatusType getName() {
    return BatchCleanupStatusType.DISABLE_DEFINITION;
  }

  /**
   * Function to disable a definition from AppConnect and Update DB
   *
   * @param definitionDetails         The details of the definition to be disabled
   * @param disableDeleteWorkflowTask The predefined task that used for disabling the definition
   */
  private void disableDefinition(DefinitionDetails definitionDetails,
      DisableDeleteWorkflowTask disableDeleteWorkflowTask) {
    try {
      WorkflowLogger.logInfo("step=definitionDisableStarted, definitionId=%s",
          definitionDetails.getDefinitionId());

      disableDeleteWorkflowTask.disableDefinitions(definitionDetails, Long.toString(
          batchJobInitContext.refreshTicketToContext(definitionDetails).getOwnerId()));

      WorkflowLogger.logInfo("step=definitionDisableComplete, status=SUCCESS definitionId=%s",
          definitionDetails.getDefinitionId());
    } catch (Exception e) {
      // In case workflow is not found in app-connect, disable it in WAS DB
      if (e.getMessage().contains(WorkflowConstants.RESOURCE_NOT_FOUND)) {
        definitionDetailsRepository.updateInternalStatus(null,
            Collections.singletonList(definitionDetails.getDefinitionId()));
        WorkflowLogger.logInfo(
            "step=definitionDisableFailure Workflow does not exist in app connect. Updating status in DB definitionId=%s",
            definitionDetails.getDefinitionId());
        return;
      }

      metricLogger.logErrorMetric(OFFLINE_BATCH_DISABLE, Type.APPLICATION_METRIC, e);
      WorkflowLogger.logError(
          "step=definitionDisableComplete, status=ERROR definitionId=%s error=%s",
          definitionDetails.getDefinitionId(), e);
      throw e;
    }
  }
}
