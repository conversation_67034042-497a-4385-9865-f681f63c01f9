package com.intuit.appintgwkflw.wkflautomate.was.batch.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.config.ThreadPool;
import lombok.Data;
import java.util.List;

@Data
/**
 * <AUTHOR>
 * Step level configuration for batch framework
 */
public class BatchJobConfigDetails {
  private boolean enabled;
  private String templateName;
  private List<String> cleanupStatus;
  private String recordTypes;
  private boolean cleanErrorProcess;
  private boolean cleanActiveProcess;
  private int baseTimeJobStart;
  private int durationInMinutes;
  private int priority;
  private String templateVersion;
  private String definitionType;
  private String templateIds;
  private ThreadPool threadPool;
  private String workflows;
  private int batchSize = 0;
  private int chunkSize = 0;
}
