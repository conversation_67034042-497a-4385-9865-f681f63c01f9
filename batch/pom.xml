<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <version>1.1.20</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>was-batch</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
            <exclusions>
            	<exclusion>
            		<groupId>org.codehaus.jettison</groupId>
            		<artifactId>jettison</artifactId>
            	</exclusion>
            </exclusions> 
        </dependency>
        <dependency>
    		<groupId>org.codehaus.jettison</groupId>
    		<artifactId>jettison</artifactId>
    		<version>1.5.4</version>
    	</dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>was-dataaccess</artifactId>
            <version>${project.version}</version>
        </dependency>
      <dependency>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <artifactId>was-core</artifactId>
        <version>${project.version}</version>
        <scope>compile</scope>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <scope>test</scope>
      </dependency>

      <!-- library for testing using env variables -->
      <dependency>
        <groupId>uk.org.webcompere</groupId>
        <artifactId>system-stubs-junit4</artifactId>
        <scope>test</scope>
      </dependency>

    </dependencies>

</project>
